# Enhanced Football Data Fetching Functions for Supabase

This repository contains enhanced Supabase Edge Functions for fetching comprehensive football data from the 365scores API and populating your database with matches, teams, leagues, countries, and players.

## 🚀 Functions Overview

### 1. Enhanced Main Data Function (`fetchdata_function_edge.txt`)
**Purpose**: Fetches and inserts countries, leagues, teams, and matches data.

**Key Improvements**:
- ✅ Fixed foreign key constraint issues
- ✅ Enhanced error handling and logging
- ✅ Comprehensive field mapping
- ✅ Data validation and type safety
- ✅ Progress tracking and detailed reporting

**Usage**:
```bash
curl -X POST https://your-project.supabase.co/functions/v1/fetchdata_function_edge
```

### 2. Enhanced Players Function (`fetch_players_function_edge.txt`)
**Purpose**: Fetches and inserts player data for all teams in the database.

**Key Improvements**:
- ✅ Fixed team_id mapping issues (uses internal team_id, not api_team_id)
- ✅ Better player filtering (excludes coaches and staff)
- ✅ Comprehensive field mapping
- ✅ Batch processing with respectful API delays
- ✅ Detailed progress reporting

**Usage**:
```bash
curl -X POST https://your-project.supabase.co/functions/v1/fetch_players_function_edge
```

### 3. Master Pipeline Function (`master_football_data_function.txt`)
**Purpose**: Orchestrates the complete data fetching process in the correct order.

**Features**:
- 🔄 Runs main data fetch first, then players data
- ⚙️ Configurable options via query parameters
- 📊 Comprehensive reporting
- 🛡️ Error recovery and partial success handling

**Usage**:
```bash
# Full pipeline (recommended)
curl -X POST https://your-project.supabase.co/functions/v1/master_football_data_function

# Skip players data
curl -X POST "https://your-project.supabase.co/functions/v1/master_football_data_function?skipPlayers=true"

# Limit number of teams for players (for testing)
curl -X POST "https://your-project.supabase.co/functions/v1/master_football_data_function?maxTeams=10"
```

### 4. Data Validation Function (`data_validation_logging_function.txt`)
**Purpose**: Validates database integrity and generates comprehensive reports.

**Features**:
- 🔍 Validates all data tables
- 🔗 Checks foreign key relationships
- 📈 Generates data quality reports
- 💡 Provides recommendations for improvements

**Usage**:
```bash
curl -X POST https://your-project.supabase.co/functions/v1/data_validation_logging_function
```

## 📋 Recommended Usage Workflow

### For First-Time Setup:
1. **Run Master Pipeline**: `master_football_data_function`
2. **Validate Results**: `data_validation_logging_function`
3. **Fix any issues** based on validation report

### For Regular Updates:
1. **Update Main Data**: `fetchdata_function_edge`
2. **Update Players**: `fetch_players_function_edge`
3. **Validate**: `data_validation_logging_function`

### For Testing/Development:
1. **Limited Test**: `master_football_data_function?maxTeams=5`
2. **Validate**: `data_validation_logging_function`

## 🔧 Key Fixes and Improvements

### Database Schema Compatibility
- ✅ Proper foreign key relationships
- ✅ Correct data types and null handling
- ✅ All required fields mapped

### API Data Handling
- ✅ Robust error handling for API failures
- ✅ Respectful rate limiting (200-300ms delays)
- ✅ Comprehensive data validation
- ✅ Graceful handling of missing data

### Performance Optimizations
- ✅ Batch processing for large datasets
- ✅ Parallel operations where safe
- ✅ Efficient foreign key validation
- ✅ Progress tracking and logging

### Error Recovery
- ✅ Partial success handling
- ✅ Detailed error reporting
- ✅ Continuation after individual failures
- ✅ Comprehensive logging for debugging

## 📊 Expected Data Volumes

Based on the 365scores API:
- **Countries**: ~100-150 countries
- **Leagues**: ~200-500 leagues/competitions
- **Teams**: ~1,000-5,000 teams (depending on data scope)
- **Matches**: ~1,000-10,000 matches (depending on date range)
- **Players**: ~25,000-100,000 players (25-30 players per team)

## 🚨 Important Notes

### Database Prerequisites
Ensure your Supabase database has the correct schema as defined in `SQL schema.txt`. Key tables needed:
- `countries`
- `leagues` 
- `teams`
- `matches`
- `players`

### Environment Variables
Set these in your Supabase Edge Functions environment:
- `SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY`

### API Rate Limiting
The functions include respectful delays to avoid overwhelming the 365scores API:
- 200ms between individual requests
- 1000ms between batches
- Configurable limits for testing

### Foreign Key Dependencies
The functions respect the following insertion order:
1. Countries (no dependencies)
2. Leagues (depends on countries)
3. Teams (depends on countries)
4. Matches (depends on leagues and teams)
5. Players (depends on teams)

## 🔍 Monitoring and Debugging

### Success Indicators
- ✅ All functions return `success: true`
- ✅ Validation function shows no critical issues
- ✅ Expected data volumes are achieved
- ✅ No foreign key constraint violations

### Common Issues and Solutions

**Issue**: "Foreign key constraint violation"
**Solution**: Run functions in correct order (use master pipeline)

**Issue**: "No teams found for players"
**Solution**: Run main data function first

**Issue**: "API rate limit exceeded"
**Solution**: Functions include delays, but you may need to increase them

**Issue**: "Missing required fields"
**Solution**: Check validation report for specific field issues

## 📈 Performance Tips

1. **Use Master Pipeline**: Most efficient for complete data refresh
2. **Limit During Testing**: Use `maxTeams` parameter for development
3. **Monitor Progress**: Check logs for real-time progress updates
4. **Validate Regularly**: Run validation after major data updates
5. **Handle Partial Failures**: Functions continue processing even if some items fail

## 🎯 Next Steps

After successful data insertion:
1. Set up regular data refresh schedules
2. Implement incremental updates for efficiency
3. Add custom business logic for your specific use case
4. Consider adding data transformation functions
5. Set up monitoring and alerting for data quality

## 📞 Support

If you encounter issues:
1. Check the validation function output first
2. Review the detailed logs in Supabase Edge Functions
3. Verify your database schema matches requirements
4. Ensure proper environment variables are set

The enhanced functions provide comprehensive logging and error reporting to help diagnose and resolve any issues quickly.
