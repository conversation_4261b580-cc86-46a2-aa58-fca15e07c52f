-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.achievements (
  achievement_id integer NOT NULL DEFAULT nextval('achievements_achievement_id_seq'::regclass),
  achievement_name character varying NOT NULL UNIQUE,
  description text NOT NULL,
  icon_url character varying,
  CONSTRAINT achievements_pkey PRIMARY KEY (achievement_id)
);
CREATE TABLE public.admin_auth_tokens (
  token_id integer NOT NULL DEFAULT nextval('admin_auth_tokens_token_id_seq'::regclass),
  admin_id integer NOT NULL,
  token_hash character varying NOT NULL UNIQUE,
  expires_at timestamp with time zone NOT NULL,
  CONSTRAINT admin_auth_tokens_pkey PRIMARY KEY (token_id),
  CONSTRAINT admin_auth_tokens_admin_id_fkey FOREIGN KEY (admin_id) REFERENCES public.admins(admin_id)
);
CREATE TABLE public.admins (
  admin_id integer NOT NULL DEFAULT nextval('admins_admin_id_seq'::regclass),
  username character varying UNIQUE,
  email character varying NOT NULL UNIQUE,
  password_hash character varying NOT NULL,
  role character varying NOT NULL,
  is_active boolean DEFAULT true,
  last_login timestamp with time zone,
  CONSTRAINT admins_pkey PRIMARY KEY (admin_id)
);
CREATE TABLE public.analyses (
  analysis_id bigint NOT NULL DEFAULT nextval('analyses_analysis_id_seq'::regclass),
  match_id integer NOT NULL,
  author_id uuid NOT NULL,
  title character varying NOT NULL,
  content_text text NOT NULL,
  content_audio_url character varying,
  publish_date timestamp with time zone DEFAULT now(),
  likes_count integer DEFAULT 0,
  comments_count integer DEFAULT 0,
  shares_count integer DEFAULT 0,
  saves_count integer DEFAULT 0,
  is_premium boolean DEFAULT false,
  status USER-DEFINED DEFAULT 'Published'::analysis_status_enum,
  CONSTRAINT analyses_pkey PRIMARY KEY (analysis_id),
  CONSTRAINT analyses_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id),
  CONSTRAINT analyses_author_id_fkey FOREIGN KEY (author_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.audit_logs (
  log_id bigint NOT NULL DEFAULT nextval('audit_log_log_id_seq'::regclass),
  actor_type character varying NOT NULL,
  actor_id integer NOT NULL,
  action character varying NOT NULL,
  target_type character varying,
  target_id integer,
  details jsonb,
  timestamp timestamp with time zone DEFAULT now(),
  CONSTRAINT audit_logs_pkey PRIMARY KEY (log_id)
);
CREATE TABLE public.comments (
  comment_id bigint NOT NULL DEFAULT nextval('comments_comment_id_seq'::regclass),
  post_id bigint NOT NULL,
  user_id uuid NOT NULL,
  comment_text text NOT NULL,
  status USER-DEFINED DEFAULT 'Published'::comment_status_enum,
  timestamp timestamp with time zone DEFAULT now(),
  parent_comment_id bigint,
  CONSTRAINT comments_pkey PRIMARY KEY (comment_id),
  CONSTRAINT comments_parent_comment_id_fkey FOREIGN KEY (parent_comment_id) REFERENCES public.comments(comment_id),
  CONSTRAINT comments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id),
  CONSTRAINT comments_post_id_fkey FOREIGN KEY (post_id) REFERENCES public.posts(post_id)
);
CREATE TABLE public.countries (
  country_id integer NOT NULL,
  name character varying NOT NULL,
  name_for_url character varying,
  sport_types ARRAY,
  image_version integer,
  is_international boolean DEFAULT false,
  total_games integer DEFAULT 0,
  live_games integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT countries_pkey PRIMARY KEY (country_id)
);
CREATE TABLE public.follows (
  follower_id uuid NOT NULL,
  follow_type USER-DEFINED NOT NULL,
  external_handle character varying,
  follow_date timestamp with time zone DEFAULT now(),
  followed_user_id uuid,
  followed_team_id integer,
  followed_player_id integer,
  follow_id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  CONSTRAINT follows_pkey PRIMARY KEY (follow_id),
  CONSTRAINT follows_follower_id_fkey FOREIGN KEY (follower_id) REFERENCES public.profiles(id),
  CONSTRAINT follows_followed_user_id_fkey FOREIGN KEY (followed_user_id) REFERENCES public.profiles(id),
  CONSTRAINT follows_followed_team_id_fkey FOREIGN KEY (followed_team_id) REFERENCES public.teams(team_id),
  CONSTRAINT follows_followed_player_id_fkey FOREIGN KEY (followed_player_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.formation_positions (
  formation_position_id integer NOT NULL,
  name character varying NOT NULL,
  short_name character varying,
  order_num integer,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT formation_positions_pkey PRIMARY KEY (formation_position_id)
);
CREATE TABLE public.league_standings (
  standing_id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  competition_id integer,
  season_num integer,
  stage_num integer,
  team_id integer,
  position integer,
  games_played integer DEFAULT 0,
  games_won integer DEFAULT 0,
  games_lost integer DEFAULT 0,
  games_even integer DEFAULT 0,
  goals_for integer DEFAULT 0,
  goals_against integer DEFAULT 0,
  goal_ratio numeric DEFAULT 0.0,
  points numeric DEFAULT 0.0,
  strike integer DEFAULT 0,
  games_ot integer DEFAULT 0,
  games_won_on_ot integer DEFAULT 0,
  games_won_on_pen integer DEFAULT 0,
  games_loss_on_ot integer DEFAULT 0,
  games_loss_on_pen integer DEFAULT 0,
  pct character varying,
  trend integer DEFAULT 0,
  next_match_id integer,
  destination_num integer,
  has_points_deduction boolean DEFAULT false,
  ppg numeric DEFAULT 0.0,
  oppg numeric DEFAULT 0.0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT league_standings_pkey PRIMARY KEY (standing_id),
  CONSTRAINT league_standings_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(team_id),
  CONSTRAINT league_standings_next_match_id_fkey FOREIGN KEY (next_match_id) REFERENCES public.matches(match_id)
);
CREATE TABLE public.leagues (
  league_id integer NOT NULL DEFAULT nextval('leagues_league_id_seq'::regclass),
  league_name character varying NOT NULL,
  logo_url character varying,
  api_league_id integer UNIQUE,
  country_id integer,
  short_name character varying,
  name_for_url character varying,
  popularity_rank bigint,
  image_version integer,
  current_season_num integer,
  current_stage_num integer,
  has_standings boolean DEFAULT false,
  has_brackets boolean DEFAULT false,
  has_stats boolean DEFAULT false,
  has_transfers boolean DEFAULT false,
  color character varying,
  is_international boolean DEFAULT false,
  is_active boolean DEFAULT true,
  CONSTRAINT leagues_pkey PRIMARY KEY (league_id)
);
CREATE TABLE public.likes (
  like_id bigint NOT NULL DEFAULT nextval('likes_like_id_seq'::regclass),
  user_id uuid NOT NULL,
  target_id bigint NOT NULL,
  timestamp timestamp with time zone DEFAULT now(),
  target_type USER-DEFINED NOT NULL,
  CONSTRAINT likes_pkey PRIMARY KEY (like_id),
  CONSTRAINT likes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.lineup_players (
  lineup_player_id bigint NOT NULL DEFAULT nextval('lineup_players_lineup_player_id_seq'::regclass),
  lineup_id bigint NOT NULL,
  player_id integer NOT NULL,
  position_name text NOT NULL,
  CONSTRAINT lineup_players_pkey PRIMARY KEY (lineup_player_id),
  CONSTRAINT lineup_players_lineup_id_fkey FOREIGN KEY (lineup_id) REFERENCES public.post_lineups(lineup_id),
  CONSTRAINT lineup_players_player_id_fkey FOREIGN KEY (player_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.linked_social_accounts (
  link_id integer NOT NULL DEFAULT nextval('linked_social_accounts_link_id_seq'::regclass),
  user_id uuid,
  team_id integer,
  network_id integer NOT NULL,
  access_token character varying,
  refresh_token character varying,
  expires_at timestamp with time zone,
  linked_date timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  CONSTRAINT linked_social_accounts_pkey PRIMARY KEY (link_id),
  CONSTRAINT linked_social_accounts_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(team_id),
  CONSTRAINT linked_social_accounts_network_id_fkey FOREIGN KEY (network_id) REFERENCES public.social_networks(network_id),
  CONSTRAINT linked_social_accounts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.match_chat_messages (
  message_id bigint NOT NULL DEFAULT nextval('match_chat_messages_message_id_seq'::regclass),
  match_id integer NOT NULL,
  user_id uuid NOT NULL,
  message_text text NOT NULL,
  timestamp timestamp with time zone DEFAULT now(),
  CONSTRAINT match_chat_messages_pkey PRIMARY KEY (message_id),
  CONSTRAINT match_chat_messages_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id),
  CONSTRAINT match_chat_messages_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.match_predictions (
  prediction_id bigint NOT NULL,
  match_id integer NOT NULL,
  type integer,
  title character varying,
  show_votes boolean DEFAULT false,
  total_votes integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  user_id uuid,
  CONSTRAINT match_predictions_pkey PRIMARY KEY (prediction_id),
  CONSTRAINT match_predictions_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id),
  CONSTRAINT match_predictions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.match_ratings (
  rating_id bigint NOT NULL DEFAULT nextval('match_ratings_rating_id_seq'::regclass),
  user_id uuid NOT NULL,
  match_id integer NOT NULL,
  rating_value numeric CHECK (rating_value >= 1.0 AND rating_value <= 10.0),
  timestamp timestamp with time zone DEFAULT now(),
  CONSTRAINT match_ratings_pkey PRIMARY KEY (rating_id),
  CONSTRAINT match_ratings_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id),
  CONSTRAINT match_ratings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.match_stages (
  stage_id integer NOT NULL,
  match_id integer NOT NULL,
  name character varying,
  short_name character varying,
  home_competitor_score numeric,
  away_competitor_score numeric,
  is_current boolean DEFAULT false,
  CONSTRAINT match_stages_pkey PRIMARY KEY (stage_id, match_id),
  CONSTRAINT match_stages_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id)
);
CREATE TABLE public.match_top_performers (
  performer_id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  match_id integer NOT NULL,
  category character varying,
  team_type character varying,
  player_id integer,
  athlete_id integer,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT match_top_performers_pkey PRIMARY KEY (performer_id),
  CONSTRAINT match_top_performers_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id),
  CONSTRAINT match_top_performers_player_id_fkey FOREIGN KEY (player_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.match_tv_networks (
  match_id integer NOT NULL,
  network_id integer NOT NULL,
  CONSTRAINT match_tv_networks_pkey PRIMARY KEY (network_id, match_id),
  CONSTRAINT match_tv_networks_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id),
  CONSTRAINT match_tv_networks_network_id_fkey FOREIGN KEY (network_id) REFERENCES public.tv_networks(network_id)
);
CREATE TABLE public.matches (
  match_id integer NOT NULL DEFAULT nextval('matches_match_id_seq'::regclass),
  home_team_id integer NOT NULL,
  away_team_id integer NOT NULL,
  league_id integer,
  match_date date,
  match_time time without time zone,
  status USER-DEFINED DEFAULT 'Upcoming'::match_status_enum,
  home_score integer,
  away_score integer,
  minute integer,
  api_match_id integer UNIQUE,
  sport_id integer,
  competition_id integer,
  season_num integer,
  stage_num integer,
  round_num integer,
  round_name character varying,
  competition_display_name character varying,
  start_time timestamp with time zone,
  status_group integer,
  status_text character varying,
  short_status_text character varying,
  game_time_display character varying,
  just_ended boolean DEFAULT false,
  game_time numeric,
  show_countdown boolean DEFAULT false,
  has_lineups boolean DEFAULT false,
  has_missing_players boolean DEFAULT false,
  has_field_positions boolean DEFAULT false,
  has_tv_networks boolean DEFAULT false,
  has_live_streaming boolean DEFAULT false,
  has_stats boolean DEFAULT false,
  has_standings boolean DEFAULT false,
  has_brackets boolean DEFAULT false,
  has_previous_meetings boolean DEFAULT false,
  has_recent_matches boolean DEFAULT false,
  has_bets boolean DEFAULT false,
  has_player_bets boolean DEFAULT false,
  has_news boolean DEFAULT false,
  has_video boolean DEFAULT false,
  venue_id integer,
  is_home_away_inverted boolean DEFAULT false,
  winner integer DEFAULT 0,
  home_away_team_order integer DEFAULT 0,
  CONSTRAINT matches_pkey PRIMARY KEY (match_id),
  CONSTRAINT matches_competition_id_fkey FOREIGN KEY (competition_id) REFERENCES public.leagues(api_league_id),
  CONSTRAINT matches_home_team_id_fkey FOREIGN KEY (home_team_id) REFERENCES public.teams(api_team_id),
  CONSTRAINT matches_away_team_id_fkey FOREIGN KEY (away_team_id) REFERENCES public.teams(api_team_id)
);
CREATE TABLE public.messages (
  message_id bigint NOT NULL DEFAULT nextval('messages_message_id_seq'::regclass),
  sender_id uuid NOT NULL,
  receiver_id uuid NOT NULL,
  message_text text NOT NULL,
  timestamp timestamp with time zone DEFAULT now(),
  is_read boolean DEFAULT false,
  CONSTRAINT messages_pkey PRIMARY KEY (message_id),
  CONSTRAINT messages_sender_id_fkey FOREIGN KEY (sender_id) REFERENCES public.profiles(id),
  CONSTRAINT messages_receiver_id_fkey FOREIGN KEY (receiver_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.news_articles (
  article_id bigint NOT NULL DEFAULT nextval('news_articles_article_id_seq'::regclass),
  title character varying NOT NULL,
  summary text NOT NULL,
  full_content text NOT NULL,
  author_id uuid,
  publish_date timestamp with time zone DEFAULT now(),
  category_id integer,
  source_id integer,
  image_url character varying,
  is_premium boolean DEFAULT false,
  is_long_read boolean DEFAULT false,
  source_social_network_id integer,
  external_article_id character varying,
  CONSTRAINT news_articles_pkey PRIMARY KEY (article_id),
  CONSTRAINT news_articles_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.news_categories(category_id),
  CONSTRAINT news_articles_source_id_fkey FOREIGN KEY (source_id) REFERENCES public.news_sources(source_id),
  CONSTRAINT news_articles_source_social_network_id_fkey FOREIGN KEY (source_social_network_id) REFERENCES public.social_networks(network_id),
  CONSTRAINT news_articles_author_id_fkey FOREIGN KEY (author_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.news_categories (
  category_id integer NOT NULL DEFAULT nextval('news_categories_category_id_seq'::regclass),
  category_name character varying NOT NULL UNIQUE,
  CONSTRAINT news_categories_pkey PRIMARY KEY (category_id)
);
CREATE TABLE public.news_sources (
  source_id integer NOT NULL DEFAULT nextval('news_sources_source_id_seq'::regclass),
  source_name character varying UNIQUE,
  source_type USER-DEFINED NOT NULL,
  CONSTRAINT news_sources_pkey PRIMARY KEY (source_id)
);
CREATE TABLE public.notifications (
  notification_id bigint NOT NULL DEFAULT nextval('notifications_notification_id_seq'::regclass),
  user_id uuid NOT NULL,
  type USER-DEFINED NOT NULL,
  content text NOT NULL,
  timestamp timestamp with time zone DEFAULT now(),
  is_read boolean DEFAULT false,
  related_post_id bigint,
  related_comment_id bigint,
  related_user_id uuid,
  CONSTRAINT notifications_pkey PRIMARY KEY (notification_id),
  CONSTRAINT notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id),
  CONSTRAINT notifications_related_post_id_fkey FOREIGN KEY (related_post_id) REFERENCES public.posts(post_id),
  CONSTRAINT notifications_related_comment_id_fkey FOREIGN KEY (related_comment_id) REFERENCES public.comments(comment_id),
  CONSTRAINT notifications_related_user_id_fkey FOREIGN KEY (related_user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.performer_stats (
  stat_id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  performer_id bigint NOT NULL,
  stat_type integer,
  stat_name character varying,
  stat_value character varying,
  CONSTRAINT performer_stats_pkey PRIMARY KEY (stat_id),
  CONSTRAINT performer_stats_performer_id_fkey FOREIGN KEY (performer_id) REFERENCES public.match_top_performers(performer_id)
);
CREATE TABLE public.player_attributes (
  player_id integer NOT NULL,
  overall_rating integer,
  pace integer,
  shooting integer,
  passing integer,
  dribbling integer,
  defending integer,
  physicality integer,
  last_updated timestamp with time zone DEFAULT now(),
  CONSTRAINT player_attributes_pkey PRIMARY KEY (player_id),
  CONSTRAINT player_attributes_player_id_fkey FOREIGN KEY (player_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.player_ratings (
  player_rating_id bigint NOT NULL DEFAULT nextval('player_ratings_player_rating_id_seq'::regclass),
  analysis_id bigint NOT NULL,
  player_id integer NOT NULL,
  rating_value numeric CHECK (rating_value >= 1.0 AND rating_value <= 10.0),
  CONSTRAINT player_ratings_pkey PRIMARY KEY (player_rating_id),
  CONSTRAINT player_ratings_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(analysis_id),
  CONSTRAINT player_ratings_player_id_fkey FOREIGN KEY (player_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.players (
  player_id integer NOT NULL DEFAULT nextval('players_player_id_seq'::regclass),
  player_name character varying NOT NULL,
  team_id integer,
  position character varying,
  number integer,
  nationality character varying,
  image_url text,
  cover_image_url text,
  bio text,
  api_player_id integer UNIQUE,
  age integer,
  jersey_num integer,
  birthdate date,
  gender integer,
  height integer,
  short_name character varying,
  name_for_url character varying,
  sport_id integer,
  club_id integer,
  nationality_id integer,
  national_team_id integer,
  image_version integer,
  formation_position_id integer,
  status integer,
  CONSTRAINT players_pkey PRIMARY KEY (player_id),
  CONSTRAINT players_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(api_team_id)
);
CREATE TABLE public.poll_options (
  option_id bigint NOT NULL DEFAULT nextval('poll_options_option_id_seq'::regclass),
  poll_id bigint NOT NULL,
  option_text text NOT NULL,
  CONSTRAINT poll_options_pkey PRIMARY KEY (option_id),
  CONSTRAINT poll_options_poll_id_fkey FOREIGN KEY (poll_id) REFERENCES public.post_polls(poll_id)
);
CREATE TABLE public.poll_votes (
  vote_id bigint NOT NULL DEFAULT nextval('poll_votes_vote_id_seq'::regclass),
  option_id bigint NOT NULL,
  user_id uuid NOT NULL,
  CONSTRAINT poll_votes_pkey PRIMARY KEY (vote_id),
  CONSTRAINT poll_votes_option_id_fkey FOREIGN KEY (option_id) REFERENCES public.poll_options(option_id),
  CONSTRAINT poll_votes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.positions (
  position_id integer NOT NULL,
  name character varying NOT NULL,
  title character varying,
  is_staff boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT positions_pkey PRIMARY KEY (position_id)
);
CREATE TABLE public.post_lineups (
  lineup_id bigint NOT NULL DEFAULT nextval('post_lineups_lineup_id_seq'::regclass),
  post_id bigint NOT NULL,
  tactic text NOT NULL,
  team_id integer NOT NULL,
  CONSTRAINT post_lineups_pkey PRIMARY KEY (lineup_id),
  CONSTRAINT post_lineups_post_id_fkey FOREIGN KEY (post_id) REFERENCES public.posts(post_id),
  CONSTRAINT post_lineups_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(team_id)
);
CREATE TABLE public.post_polls (
  poll_id bigint NOT NULL DEFAULT nextval('post_polls_poll_id_seq'::regclass),
  post_id bigint NOT NULL,
  question text NOT NULL,
  CONSTRAINT post_polls_pkey PRIMARY KEY (poll_id),
  CONSTRAINT post_polls_post_id_fkey FOREIGN KEY (post_id) REFERENCES public.posts(post_id)
);
CREATE TABLE public.post_substitutions (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  post_id bigint NOT NULL UNIQUE,
  match_id integer NOT NULL,
  team_id integer NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT post_substitutions_pkey PRIMARY KEY (id),
  CONSTRAINT post_substitutions_post_id_fkey FOREIGN KEY (post_id) REFERENCES public.posts(post_id),
  CONSTRAINT post_substitutions_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id),
  CONSTRAINT post_substitutions_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(team_id)
);
CREATE TABLE public.posts (
  post_id bigint NOT NULL DEFAULT nextval('posts_post_id_seq'::regclass),
  user_id uuid,
  team_id integer,
  content_text text,
  content_media_url character varying,
  post_type USER-DEFINED NOT NULL,
  status USER-DEFINED DEFAULT 'Published'::post_status_enum,
  timestamp timestamp with time zone DEFAULT now(),
  likes_count integer DEFAULT 0,
  comments_count integer DEFAULT 0,
  shares_count integer DEFAULT 0,
  source_social_network_id integer,
  external_post_id character varying,
  import_timestamp timestamp with time zone,
  CONSTRAINT posts_pkey PRIMARY KEY (post_id),
  CONSTRAINT posts_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(team_id),
  CONSTRAINT posts_source_social_network_id_fkey FOREIGN KEY (source_social_network_id) REFERENCES public.social_networks(network_id),
  CONSTRAINT posts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.prediction_challenges (
  challenge_id integer NOT NULL DEFAULT nextval('prediction_challenges_challenge_id_seq'::regclass),
  match_id integer NOT NULL,
  challenge_name character varying NOT NULL,
  start_date timestamp with time zone NOT NULL,
  end_date timestamp with time zone NOT NULL,
  CONSTRAINT prediction_challenges_pkey PRIMARY KEY (challenge_id),
  CONSTRAINT prediction_challenges_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id)
);
CREATE TABLE public.prediction_options (
  option_id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  prediction_id bigint NOT NULL,
  option_num integer,
  name character varying,
  symbol integer,
  vote_count integer DEFAULT 0,
  vote_percentage integer DEFAULT 0,
  vote_key character varying,
  CONSTRAINT prediction_options_pkey PRIMARY KEY (option_id),
  CONSTRAINT prediction_options_prediction_id_fkey FOREIGN KEY (prediction_id) REFERENCES public.match_predictions(prediction_id)
);
CREATE TABLE public.profiles (
  id uuid NOT NULL,
  username character varying NOT NULL UNIQUE CHECK (char_length(username::text) >= 3),
  profile_picture_url text,
  cover_image_url text,
  bio text,
  location character varying,
  following_count integer DEFAULT 0,
  followers_count integer DEFAULT 0,
  posts_count integer DEFAULT 0,
  level integer DEFAULT 1,
  points integer DEFAULT 0,
  total_predictions integer DEFAULT 0,
  correct_predictions integer DEFAULT 0,
  prediction_accuracy numeric DEFAULT 0.00,
  ranking integer,
  role_id integer,
  updated_at timestamp with time zone DEFAULT now(),
  join_date timestamp with time zone,
  full_name text,
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id),
  CONSTRAINT profiles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(role_id)
);
CREATE TABLE public.reports (
  report_id integer NOT NULL DEFAULT nextval('reports_report_id_seq'::regclass),
  reporter_user_id uuid NOT NULL,
  reported_content_type USER-DEFINED NOT NULL,
  reason text NOT NULL,
  status USER-DEFINED DEFAULT 'Pending Review'::report_status_enum,
  report_date timestamp with time zone DEFAULT now(),
  reviewer_admin_id integer,
  notes text,
  reported_post_id bigint,
  reported_comment_id bigint,
  reported_user_id uuid,
  CONSTRAINT reports_pkey PRIMARY KEY (report_id),
  CONSTRAINT reports_reviewer_admin_id_fkey FOREIGN KEY (reviewer_admin_id) REFERENCES public.admins(admin_id),
  CONSTRAINT reports_reporter_user_id_fkey FOREIGN KEY (reporter_user_id) REFERENCES public.profiles(id),
  CONSTRAINT reports_reported_post_id_fkey FOREIGN KEY (reported_post_id) REFERENCES public.posts(post_id),
  CONSTRAINT reports_reported_comment_id_fkey FOREIGN KEY (reported_comment_id) REFERENCES public.comments(comment_id),
  CONSTRAINT reports_reported_user_id_fkey FOREIGN KEY (reported_user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.roles (
  role_id integer GENERATED ALWAYS AS IDENTITY NOT NULL,
  role_name character varying NOT NULL UNIQUE,
  permissions jsonb,
  CONSTRAINT roles_pkey PRIMARY KEY (role_id)
);
CREATE TABLE public.saved_articles (
  saved_article_id bigint NOT NULL DEFAULT nextval('saved_articles_saved_article_id_seq'::regclass),
  user_id uuid NOT NULL,
  article_id bigint NOT NULL,
  saved_date timestamp with time zone DEFAULT now(),
  CONSTRAINT saved_articles_pkey PRIMARY KEY (saved_article_id),
  CONSTRAINT saved_articles_article_id_fkey FOREIGN KEY (article_id) REFERENCES public.news_articles(article_id),
  CONSTRAINT saved_articles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.search_queries (
  query_id bigint NOT NULL DEFAULT nextval('search_queries_query_id_seq'::regclass),
  user_id uuid,
  query_text character varying NOT NULL,
  timestamp timestamp with time zone DEFAULT now(),
  CONSTRAINT search_queries_pkey PRIMARY KEY (query_id),
  CONSTRAINT search_queries_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.social_imports_log (
  import_id bigint NOT NULL DEFAULT nextval('social_imports_log_import_id_seq'::regclass),
  network_id integer NOT NULL,
  entity_id integer NOT NULL,
  entity_type USER-DEFINED NOT NULL,
  status USER-DEFINED NOT NULL,
  fetched_items_count integer DEFAULT 0,
  error_message text,
  import_timestamp timestamp with time zone DEFAULT now(),
  CONSTRAINT social_imports_log_pkey PRIMARY KEY (import_id),
  CONSTRAINT social_imports_log_network_id_fkey FOREIGN KEY (network_id) REFERENCES public.social_networks(network_id)
);
CREATE TABLE public.social_networks (
  network_id integer NOT NULL DEFAULT nextval('social_networks_network_id_seq'::regclass),
  network_name character varying NOT NULL UNIQUE,
  api_base_url character varying,
  description text,
  CONSTRAINT social_networks_pkey PRIMARY KEY (network_id)
);
CREATE TABLE public.sports (
  sport_id integer NOT NULL,
  name character varying NOT NULL,
  name_for_url character varying,
  draw_support boolean DEFAULT false,
  image_version integer,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT sports_pkey PRIMARY KEY (sport_id)
);
CREATE TABLE public.stories (
  story_id bigint NOT NULL DEFAULT nextval('stories_story_id_seq'::regclass),
  user_id uuid,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  expires_at timestamp with time zone NOT NULL,
  author_type USER-DEFINED NOT NULL,
  team_id integer,
  CONSTRAINT stories_pkey PRIMARY KEY (story_id),
  CONSTRAINT stories_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id),
  CONSTRAINT stories_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(team_id)
);
CREATE TABLE public.story_items (
  item_id bigint NOT NULL DEFAULT nextval('story_items_item_id_seq'::regclass),
  story_id bigint NOT NULL,
  media_url character varying NOT NULL,
  duration integer,
  order smallint NOT NULL,
  CONSTRAINT story_items_pkey PRIMARY KEY (item_id),
  CONSTRAINT story_items_story_id_fkey FOREIGN KEY (story_id) REFERENCES public.stories(story_id)
);
CREATE TABLE public.substitution_suggestions (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  substitution_post_id bigint NOT NULL,
  player_out_id integer NOT NULL,
  player_in_id integer NOT NULL,
  CONSTRAINT substitution_suggestions_pkey PRIMARY KEY (id),
  CONSTRAINT substitution_suggestions_substitution_post_id_fkey FOREIGN KEY (substitution_post_id) REFERENCES public.post_substitutions(id),
  CONSTRAINT substitution_suggestions_player_out_id_fkey FOREIGN KEY (player_out_id) REFERENCES public.players(player_id),
  CONSTRAINT substitution_suggestions_player_in_id_fkey FOREIGN KEY (player_in_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.system_configs (
  setting_key character varying NOT NULL,
  setting_value text NOT NULL,
  description text,
  CONSTRAINT system_configs_pkey PRIMARY KEY (setting_key)
);
CREATE TABLE public.teams (
  team_id integer NOT NULL DEFAULT nextval('teams_team_id_seq'::regclass),
  team_name character varying NOT NULL,
  logo_url character varying,
  is_official boolean DEFAULT false,
  league_id integer,
  established integer,
  website text,
  stadium_image_url text,
  about_description text,
  api_team_id integer UNIQUE,
  country_id integer,
  short_name character varying,
  symbolic_name character varying,
  name_for_url character varying,
  popularity_rank bigint,
  image_version integer,
  color character varying,
  away_color character varying,
  main_competition_id integer,
  has_squad boolean DEFAULT false,
  has_transfers boolean DEFAULT false,
  competitor_num integer,
  hide_on_search boolean DEFAULT false,
  hide_on_catalog boolean DEFAULT false,
  CONSTRAINT teams_pkey PRIMARY KEY (team_id),
  CONSTRAINT teams_league_id_fkey FOREIGN KEY (league_id) REFERENCES public.leagues(league_id)
);
CREATE TABLE public.transactional_emails (
  email_id integer NOT NULL DEFAULT nextval('transactional_emails_email_id_seq'::regclass),
  recipient_email character varying NOT NULL,
  subject character varying NOT NULL,
  type USER-DEFINED NOT NULL,
  status USER-DEFINED DEFAULT 'Sent'::transactional_email_status_enum,
  verification_code character varying,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT transactional_emails_pkey PRIMARY KEY (email_id)
);
CREATE TABLE public.transfers (
  transfer_id bigint NOT NULL DEFAULT nextval('transfers_transfer_id_seq'::regclass),
  player_id integer NOT NULL,
  from_team_id integer,
  to_team_id integer,
  transfer_fee text,
  transfer_date date,
  status USER-DEFINED NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT transfers_pkey PRIMARY KEY (transfer_id),
  CONSTRAINT transfers_player_id_fkey FOREIGN KEY (player_id) REFERENCES public.players(player_id),
  CONSTRAINT transfers_from_team_id_fkey FOREIGN KEY (from_team_id) REFERENCES public.teams(team_id),
  CONSTRAINT transfers_to_team_id_fkey FOREIGN KEY (to_team_id) REFERENCES public.teams(team_id)
);
CREATE TABLE public.tv_networks (
  network_id integer NOT NULL,
  type integer,
  name character varying NOT NULL,
  country_id integer,
  website character varying,
  bookmaker_id integer,
  image_version integer,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT tv_networks_pkey PRIMARY KEY (network_id),
  CONSTRAINT tv_networks_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.countries(country_id)
);
CREATE TABLE public.user_achievements (
  user_achievement_id bigint NOT NULL DEFAULT nextval('user_achievements_user_achievement_id_seq'::regclass),
  user_id uuid NOT NULL,
  achievement_id integer NOT NULL,
  date_achieved timestamp with time zone DEFAULT now(),
  CONSTRAINT user_achievements_pkey PRIMARY KEY (user_achievement_id),
  CONSTRAINT user_achievements_achievement_id_fkey FOREIGN KEY (achievement_id) REFERENCES public.achievements(achievement_id),
  CONSTRAINT user_achievements_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.user_activity_stats (
  user_id uuid NOT NULL,
  posts_this_week integer DEFAULT 0,
  comments_this_week integer DEFAULT 0,
  likes_given_this_week integer DEFAULT 0,
  predictions_made_this_week integer DEFAULT 0,
  last_updated timestamp with time zone DEFAULT now(),
  CONSTRAINT user_activity_stats_pkey PRIMARY KEY (user_id),
  CONSTRAINT user_activity_stats_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.user_login_history (
  login_id bigint NOT NULL DEFAULT nextval('user_login_history_login_id_seq'::regclass),
  user_id uuid,
  email_attempt character varying,
  ip_address character varying NOT NULL,
  user_agent text NOT NULL,
  country character varying,
  city character varying,
  os character varying,
  device_type character varying,
  status USER-DEFINED NOT NULL,
  login_timestamp timestamp with time zone DEFAULT now(),
  CONSTRAINT user_login_history_pkey PRIMARY KEY (login_id),
  CONSTRAINT user_login_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.user_predictions (
  user_prediction_id bigint NOT NULL DEFAULT nextval('user_predictions_user_prediction_id_seq'::regclass),
  user_id uuid NOT NULL,
  challenge_id integer,
  predicted_home_score integer NOT NULL,
  predicted_away_score integer NOT NULL,
  prediction_status USER-DEFINED DEFAULT 'Pending'::prediction_status_enum,
  prediction_date timestamp with time zone DEFAULT now(),
  match_id integer NOT NULL,
  CONSTRAINT user_predictions_pkey PRIMARY KEY (user_prediction_id),
  CONSTRAINT user_predictions_challenge_id_fkey FOREIGN KEY (challenge_id) REFERENCES public.prediction_challenges(challenge_id),
  CONSTRAINT user_predictions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id),
  CONSTRAINT user_predictions_match_id_fkey FOREIGN KEY (match_id) REFERENCES public.matches(match_id)
);
CREATE TABLE public.user_preferences (
  user_id uuid NOT NULL,
  enable_context_based_auth boolean DEFAULT true,
  enable_email_notifications boolean DEFAULT true,
  theme USER-DEFINED DEFAULT 'Light'::theme_enum,
  language character varying DEFAULT 'en'::character varying,
  CONSTRAINT user_preferences_pkey PRIMARY KEY (user_id),
  CONSTRAINT user_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.user_trusted_devices (
  trusted_device_id bigint NOT NULL DEFAULT nextval('user_trusted_devices_trusted_device_id_seq'::regclass),
  user_id uuid NOT NULL,
  device_signature character varying NOT NULL UNIQUE,
  last_used_timestamp timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_trusted_devices_pkey PRIMARY KEY (trusted_device_id),
  CONSTRAINT user_trusted_devices_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.venues (
  venue_id integer NOT NULL,
  name character varying NOT NULL,
  short_name character varying,
  capacity integer,
  city character varying,
  country_id integer,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT venues_pkey PRIMARY KEY (venue_id),
  CONSTRAINT venues_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.countries(country_id)
);
CREATE TABLE public.video_categories (
  category_id integer NOT NULL DEFAULT nextval('video_categories_category_id_seq'::regclass),
  category_name character varying NOT NULL UNIQUE,
  CONSTRAINT video_categories_pkey PRIMARY KEY (category_id)
);
CREATE TABLE public.videos (
  video_id bigint NOT NULL DEFAULT nextval('videos_video_id_seq'::regclass),
  title character varying NOT NULL,
  description text,
  url character varying NOT NULL,
  thumbnail_url character varying,
  category_id integer,
  uploader_id uuid,
  views_count integer DEFAULT 0,
  likes_count integer DEFAULT 0,
  upload_date timestamp with time zone DEFAULT now(),
  source_social_network_id integer,
  external_video_id character varying,
  video_type USER-DEFINED NOT NULL DEFAULT 'long_form'::video_type_enum,
  CONSTRAINT videos_pkey PRIMARY KEY (video_id),
  CONSTRAINT videos_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.video_categories(category_id),
  CONSTRAINT videos_source_social_network_id_fkey FOREIGN KEY (source_social_network_id) REFERENCES public.social_networks(network_id),
  CONSTRAINT videos_uploader_id_fkey FOREIGN KEY (uploader_id) REFERENCES public.profiles(id)
);