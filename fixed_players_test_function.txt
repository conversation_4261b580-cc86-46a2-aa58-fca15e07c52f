// Quick Test Function for Fixed Players Insertion
// This function tests the corrected foreign key mapping for players

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Helper functions
const delay = (ms) => new Promise((res) => setTimeout(res, ms));

function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

function parseDate(dateString) {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
  } catch {
    return null;
  }
}

async function upsertPlayersFixed(supabase, players, apiTeamId) {
  if (!players || players.length === 0) {
    console.log(`No players to update for team ${apiTeamId}.`);
    return { success: true, count: 0 };
  }

  try {
    // Filter actual players (not staff)
    const actualPlayers = players.filter((p) => {
      const isPlayer = p.formationPosition && 
                      p.formationPosition.name !== 'Coach' && 
                      p.formationPosition.name !== 'Assistant Coach' &&
                      p.position && 
                      p.position.name !== 'Management' &&
                      !p.position.isStaff;
      return isPlayer;
    });

    if (actualPlayers.length === 0) {
      console.log(`No actual players found for team ${apiTeamId} after filtering.`);
      return { success: true, count: 0 };
    }

    // Verify the team exists in database
    const { data: teamData, error: teamError } = await supabase
      .from('teams')
      .select('api_team_id, team_name')
      .eq('api_team_id', apiTeamId)
      .single();

    if (teamError || !teamData) {
      console.warn(`⚠️ Team with api_team_id ${apiTeamId} not found in database. Skipping players.`);
      return { success: false, count: 0, error: 'Team not found' };
    }

    console.log(`🔧 Processing ${actualPlayers.length} players for team: ${teamData.team_name} (API ID: ${apiTeamId})`);

    // FIXED: Use api_team_id directly as per foreign key constraint
    const playersToUpsert = actualPlayers.map((player) => ({
      api_player_id: player.id,
      player_name: player.name,
      team_id: apiTeamId, // ✅ FIXED: Use api_team_id as per foreign key constraint
      position: player.position?.name || null,
      number: safeParseInt(player.jerseyNum),
      nationality: null,
      image_url: null,
      cover_image_url: null,
      bio: null,
      age: safeParseInt(player.age),
      jersey_num: safeParseInt(player.jerseyNum),
      birthdate: parseDate(player.birthdate),
      gender: safeParseInt(player.gender),
      height: safeParseInt(player.height),
      short_name: player.shortName || null,
      name_for_url: player.nameForURL || null,
      sport_id: safeParseInt(player.sportId),
      club_id: safeParseInt(player.clubId),
      nationality_id: safeParseInt(player.nationalityId),
      national_team_id: safeParseInt(player.nationalTeamId),
      image_version: safeParseInt(player.imageVersion),
      formation_position_id: safeParseInt(player.formationPosition?.id),
      status: null
    }));

    console.log(`🔧 Sample player data:`, {
      api_player_id: playersToUpsert[0]?.api_player_id,
      player_name: playersToUpsert[0]?.player_name,
      team_id: playersToUpsert[0]?.team_id,
      position: playersToUpsert[0]?.position
    });

    const { error, count } = await supabase
      .from('players')
      .upsert(playersToUpsert, { onConflict: 'api_player_id' });

    if (error) {
      console.error(`❌ Database error for team ${apiTeamId}:`, error.message);
      throw new Error(`Error upserting players for team ${apiTeamId}: ${error.message}`);
    }

    console.log(`✅ Successfully upserted ${playersToUpsert.length} players for team ${teamData.team_name}`);
    return { success: true, count: playersToUpsert.length };
  } catch (error) {
    console.error(`❌ Failed to upsert players for team ${apiTeamId}:`, error.message);
    return { success: false, count: 0, error: error.message };
  }
}

// Main handler - Test with a limited number of teams
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();
  let totalPlayersUpserted = 0;
  let teamsProcessed = 0;
  let teamsFailed = 0;
  const results = [];

  try {
    console.log('🧪 Starting FIXED players insertion test...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '', 
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get query parameters
    const url = new URL(req.url);
    const maxTeams = parseInt(url.searchParams.get('maxTeams') || '5'); // Default to 5 for testing
    const specificTeamId = url.searchParams.get('teamId'); // Test specific team

    let teamsQuery = supabase
      .from('teams')
      .select('api_team_id, team_name')
      .not('api_team_id', 'is', null);

    if (specificTeamId) {
      teamsQuery = teamsQuery.eq('api_team_id', parseInt(specificTeamId));
    } else {
      teamsQuery = teamsQuery.limit(maxTeams);
    }

    const { data: teams, error: teamsError } = await teamsQuery;

    if (teamsError) {
      throw new Error(`Failed to fetch teams: ${teamsError.message}`);
    }

    if (!teams || teams.length === 0) {
      return new Response(JSON.stringify({
        success: true,
        message: 'No teams found to test',
        results: []
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    console.log(`🎯 Testing with ${teams.length} teams...`);

    // Process each team
    for (const team of teams) {
      try {
        const teamId = team.api_team_id;
        const teamName = team.team_name;
        
        console.log(`\n🏟️ Testing team: ${teamName} (ID: ${teamId})`);
        
        const SQUAD_API_URL = `https://webws.365scores.com/web/squads/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitors=${teamId}`;
        
        const response = await fetch(SQUAD_API_URL);
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        
        if (data.squads && data.squads.length > 0 && data.squads[0].athletes) {
          const result = await upsertPlayersFixed(supabase, data.squads[0].athletes, teamId);
          
          results.push({
            teamId,
            teamName,
            success: result.success,
            playersCount: result.count,
            error: result.error
          });
          
          if (result.success) {
            totalPlayersUpserted += result.count;
            teamsProcessed++;
          } else {
            teamsFailed++;
          }
        } else {
          console.log(`   ℹ️ No squad data found for team ${teamName}`);
          results.push({
            teamId,
            teamName,
            success: true,
            playersCount: 0,
            note: 'No squad data'
          });
          teamsProcessed++;
        }
        
        // Respectful delay
        await delay(300);
        
      } catch (error) {
        console.error(`   ❌ Failed to process team ${team.team_name}:`, error.message);
        results.push({
          teamId: team.api_team_id,
          teamName: team.team_name,
          success: false,
          playersCount: 0,
          error: error.message
        });
        teamsFailed++;
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    const summary = {
      teamsTotal: teams.length,
      teamsProcessed,
      teamsFailed,
      totalPlayersUpserted,
      duration: `${duration}s`,
      successRate: `${Math.round((teamsProcessed / teams.length) * 100)}%`
    };

    console.log(`\n🎉 Test completed in ${duration}s!`);
    console.log(`📊 Results: ${teamsProcessed}/${teams.length} teams successful, ${totalPlayersUpserted} players inserted`);

    return new Response(JSON.stringify({
      success: true,
      message: 'Fixed players insertion test completed',
      summary,
      results,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.error(`❌ Test failed after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      partialResults: results,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
