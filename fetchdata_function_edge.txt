// Import necessary libraries
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};
// The main API endpoint
const API_URL = 'https://webws.365scores.com/web/games/allscores/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1&showOdds=true';
// --- Helper Functions for Database Operations ---
async function upsertCountries(supabase, countries) {
  if (!countries || countries.length === 0) return;
  const toUpsert = countries.map((c)=>({
      country_id: c.id,
      name: c.name,
      name_for_url: c.nameForURL,
      image_version: c.imageVersion,
      is_international: c.isInternational || false,
      live_games: c.liveGames || 0
    }));
  const { error } = await supabase.from('countries').upsert(toUpsert, {
    onConflict: 'country_id'
  });
  if (error) throw new Error(`Error upserting countries: ${error.message}`);
  console.log(`Upserted ${toUpsert.length} countries.`);
}
async function upsertLeagues(supabase, leagues) {
  if (!leagues || leagues.length === 0) return;
  const toUpsert = leagues.map((l)=>({
      api_league_id: l.id,
      league_name: l.name,
      country_id: l.countryId,
      short_name: l.shortName,
      name_for_url: l.nameForURL,
      popularity_rank: l.popularityRank,
      image_version: l.imageVersion,
      current_season_num: l.currentSeasonNum,
      has_standings: l.hasStandings || false,
      has_brackets: l.hasBrackets || false,
      color: l.color,
      is_international: l.isInternational || false
    }));
  const { error } = await supabase.from('leagues').upsert(toUpsert, {
    onConflict: 'api_league_id'
  });
  if (error) throw new Error(`Error upserting leagues: ${error.message}`);
  console.log(`Upserted ${toUpsert.length} leagues.`);
}
async function upsertTeams(supabase, games) {
  if (!games || games.length === 0) return;
  const teamsMap = new Map();
  games.forEach((game)=>{
    if (game.homeCompetitor) teamsMap.set(game.homeCompetitor.id, game.homeCompetitor);
    if (game.awayCompetitor) teamsMap.set(game.awayCompetitor.id, game.awayCompetitor);
  });
  if (teamsMap.size === 0) return;
  const toUpsert = Array.from(teamsMap.values()).map((t)=>({
      api_team_id: t.id,
      team_name: t.name,
      country_id: t.countryId,
      short_name: t.shortName,
      name_for_url: t.nameForURL,
      popularity_rank: t.popularityRank,
      image_version: t.imageVersion,
      color: t.color,
      away_color: t.awayColor,
      has_squad: t.hasSquad || false
    }));
  const { error } = await supabase.from('teams').upsert(toUpsert, {
    onConflict: 'api_team_id'
  });
  if (error) throw new Error(`Error upserting teams: ${error.message}`);
  console.log(`Upserted ${toUpsert.length} teams.`);
}
async function upsertMatches(supabase, games) {
  if (!games || games.length === 0) return;
  // --- ROBUSTNESS FIX ---
  // 1. Get a list of all competition IDs that already exist in our database.
  const { data: existingLeagues, error: leagueFetchError } = await supabase.from('leagues').select('api_league_id');
  if (leagueFetchError) {
    throw new Error(`Could not query existing leagues: ${leagueFetchError.message}`);
  }
  const existingLeagueIds = new Set(existingLeagues.map((l)=>l.api_league_id));
  // 2. Filter the incoming matches to only include those whose competition ID we have saved.
  const validGames = games.filter((game)=>existingLeagueIds.has(game.competitionId));
  const skippedCount = games.length - validGames.length;
  if (skippedCount > 0) {
    console.warn(`Warning: Skipped ${skippedCount} matches due to missing league/competition ID in the database.`);
  }
  if (validGames.length === 0) {
    console.log("No valid matches to insert after filtering.");
    return;
  }
  // 3. Upsert only the valid matches.
  const toUpsert = validGames.map((g)=>({
      api_match_id: g.id,
      home_team_id: g.homeCompetitor.id,
      away_team_id: g.awayCompetitor.id,
      competition_id: g.competitionId,
      start_time: g.startTime,
      status_text: g.statusText,
      short_status_text: g.shortStatusText,
      home_score: g.scores?.[0] ?? null,
      away_score: g.scores?.[1] ?? null,
      minute: g.gameTime,
      game_time_display: g.gameTimeDisplay,
      has_lineups: g.hasLineups || false,
      has_stats: g.hasStats || false,
      has_standings: g.hasStandings || false,
      has_bets: g.hasBets || false,
      has_news: g.hasNews || false,
      has_video: g.hasVideo || false
    }));
  const { error } = await supabase.from('matches').upsert(toUpsert, {
    onConflict: 'api_match_id'
  });
  if (error) throw new Error(`Error upserting matches: ${error.message}`);
  console.log(`Upserted ${toUpsert.length} valid matches.`);
}
// --- Main Edge Function Handler ---
serve(async (req)=>{
  if (req.method === 'OPTIONS') return new Response('ok', {
    headers: corsHeaders
  });
  try {
    const supabase = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '');
    console.log(`Fetching data from API...`);
    const response = await fetch(API_URL);
    if (!response.ok) throw new Error(`API request failed: ${response.status}`);
    const data = await response.json();
    console.log('Successfully fetched data.');
    // Stage 1: Upsert parent data
    console.log('Stage 1: Upserting countries and leagues...');
    await Promise.all([
      upsertCountries(supabase, data.countries || []),
      upsertLeagues(supabase, data.competitions || [])
    ]);
    // Stage 2: Upsert data that depends on Stage 1
    console.log('Stage 2: Upserting teams...');
    await upsertTeams(supabase, data.games || []);
    // Stage 3: Upsert matches, now with safety check
    console.log('Stage 3: Upserting matches...');
    await upsertMatches(supabase, data.games || []);
    console.log("Database population successful!");
    return new Response(JSON.stringify({
      message: "Database population successful!"
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });
  } catch (error) {
    console.error('An error occurred in the handler:', error.message);
    return new Response(JSON.stringify({
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
