// Import necessary libraries
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// The main API endpoint - Enhanced to get more comprehensive data
const API_URL = 'https://webws.365scores.com/web/games/allscores/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1&showOdds=true&withTop=true';

// Helper function to safely convert date strings
function parseDate(dateString) {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date.toISOString();
  } catch {
    return null;
  }
}

// Helper function to safely parse integers
function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

// Helper function to safely parse floats
function safeParseFloat(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? null : parsed;
}
// --- Enhanced Helper Functions for Database Operations ---
async function upsertCountries(supabase, countries) {
  if (!countries || countries.length === 0) {
    console.log('No countries to upsert.');
    return { success: true, count: 0 };
  }

  try {
    const toUpsert = countries.map((c) => ({
      country_id: c.id,
      name: c.name,
      name_for_url: c.nameForURL || null,
      sport_types: c.sportTypes || null,
      image_version: safeParseInt(c.imageVersion),
      is_international: c.isInternational || false,
      total_games: safeParseInt(c.totalGames) || 0,
      live_games: safeParseInt(c.liveGames) || 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    const { error, count } = await supabase
      .from('countries')
      .upsert(toUpsert, { onConflict: 'country_id' });

    if (error) throw new Error(`Error upserting countries: ${error.message}`);

    console.log(`✅ Upserted ${toUpsert.length} countries successfully.`);
    return { success: true, count: toUpsert.length };
  } catch (error) {
    console.error(`❌ Failed to upsert countries:`, error.message);
    throw error;
  }
}
async function upsertLeagues(supabase, leagues) {
  if (!leagues || leagues.length === 0) {
    console.log('No leagues to upsert.');
    return { success: true, count: 0 };
  }

  try {
    const toUpsert = leagues.map((l) => ({
      api_league_id: l.id,
      league_name: l.name,
      logo_url: null, // Not provided in this API response
      country_id: l.countryId,
      short_name: l.shortName || null,
      name_for_url: l.nameForURL || null,
      popularity_rank: safeParseInt(l.popularityRank),
      image_version: safeParseInt(l.imageVersion),
      current_season_num: safeParseInt(l.currentSeasonNum),
      current_stage_num: safeParseInt(l.currentStageNum),
      has_standings: l.hasStandings || false,
      has_brackets: l.hasBrackets || false,
      has_stats: l.hasStats || false,
      has_transfers: l.hasTransfers || false,
      color: l.color || null,
      is_international: l.isInternational || false,
      is_active: l.isActive !== false // Default to true unless explicitly false
    }));

    const { error, count } = await supabase
      .from('leagues')
      .upsert(toUpsert, { onConflict: 'api_league_id' });

    if (error) throw new Error(`Error upserting leagues: ${error.message}`);

    console.log(`✅ Upserted ${toUpsert.length} leagues successfully.`);
    return { success: true, count: toUpsert.length };
  } catch (error) {
    console.error(`❌ Failed to upsert leagues:`, error.message);
    throw error;
  }
}
async function upsertTeams(supabase, games) {
  if (!games || games.length === 0) {
    console.log('No games provided for team extraction.');
    return { success: true, count: 0 };
  }

  try {
    const teamsMap = new Map();

    // Extract teams from games
    games.forEach((game) => {
      if (game.homeCompetitor) {
        teamsMap.set(game.homeCompetitor.id, game.homeCompetitor);
      }
      if (game.awayCompetitor) {
        teamsMap.set(game.awayCompetitor.id, game.awayCompetitor);
      }
    });

    if (teamsMap.size === 0) {
      console.log('No teams found in games data.');
      return { success: true, count: 0 };
    }

    const toUpsert = Array.from(teamsMap.values()).map((t) => ({
      api_team_id: t.id,
      team_name: t.name,
      logo_url: null, // Not provided in this API response
      is_official: false, // Default value
      league_id: null, // Will be set separately if needed
      established: null, // Not provided in this API response
      website: null, // Not provided in this API response
      stadium_image_url: null, // Not provided in this API response
      about_description: null, // Not provided in this API response
      country_id: t.countryId,
      short_name: t.shortName || null,
      symbolic_name: t.symbolicName || null,
      name_for_url: t.nameForURL || null,
      popularity_rank: safeParseInt(t.popularityRank),
      image_version: safeParseInt(t.imageVersion),
      color: t.color || null,
      away_color: t.awayColor || null,
      main_competition_id: safeParseInt(t.mainCompetitionId),
      has_squad: t.hasSquad || false,
      has_transfers: t.hasTransfers || false,
      competitor_num: safeParseInt(t.competitorNum) || 0,
      hide_on_search: t.hideOnSearch || false,
      hide_on_catalog: t.hideOnCatalog || false
    }));

    const { error, count } = await supabase
      .from('teams')
      .upsert(toUpsert, { onConflict: 'api_team_id' });

    if (error) throw new Error(`Error upserting teams: ${error.message}`);

    console.log(`✅ Upserted ${toUpsert.length} teams successfully.`);
    return { success: true, count: toUpsert.length };
  } catch (error) {
    console.error(`❌ Failed to upsert teams:`, error.message);
    throw error;
  }
}
async function upsertMatches(supabase, games) {
  if (!games || games.length === 0) {
    console.log('No games to upsert.');
    return { success: true, count: 0 };
  }

  try {
    // 1. Get existing leagues and teams to validate foreign keys
    const [leaguesResult, teamsResult] = await Promise.all([
      supabase.from('leagues').select('api_league_id'),
      supabase.from('teams').select('api_team_id')
    ]);

    if (leaguesResult.error) {
      throw new Error(`Could not query existing leagues: ${leaguesResult.error.message}`);
    }
    if (teamsResult.error) {
      throw new Error(`Could not query existing teams: ${teamsResult.error.message}`);
    }

    const existingLeagueIds = new Set(leaguesResult.data.map(l => l.api_league_id));
    const existingTeamIds = new Set(teamsResult.data.map(t => t.api_team_id));

    // 2. Filter games to only include those with valid foreign keys
    const validGames = games.filter((game) => {
      const hasValidLeague = existingLeagueIds.has(game.competitionId);
      const hasValidHomeTeam = game.homeCompetitor && existingTeamIds.has(game.homeCompetitor.id);
      const hasValidAwayTeam = game.awayCompetitor && existingTeamIds.has(game.awayCompetitor.id);

      return hasValidLeague && hasValidHomeTeam && hasValidAwayTeam;
    });

    const skippedCount = games.length - validGames.length;
    if (skippedCount > 0) {
      console.warn(`⚠️ Skipped ${skippedCount} matches due to missing league/team references in database.`);
    }

    if (validGames.length === 0) {
      console.log('No valid matches to insert after filtering.');
      return { success: true, count: 0 };
    }

    // 3. Prepare match data with comprehensive field mapping
    const toUpsert = validGames.map((g) => ({
      api_match_id: g.id,
      home_team_id: g.homeCompetitor.id,
      away_team_id: g.awayCompetitor.id,
      league_id: null, // This will be set by a separate process if needed
      match_date: g.startTime ? parseDate(g.startTime)?.split('T')[0] : null,
      match_time: g.startTime ? parseDate(g.startTime)?.split('T')[1]?.split('.')[0] : null,
      status: 'Upcoming', // Default status, can be enhanced based on statusId
      home_score: g.scores?.[0] !== undefined ? safeParseInt(g.scores[0]) : null,
      away_score: g.scores?.[1] !== undefined ? safeParseInt(g.scores[1]) : null,
      minute: safeParseInt(g.gameTime),
      sport_id: safeParseInt(g.sportId),
      competition_id: g.competitionId,
      season_num: safeParseInt(g.seasonNum),
      stage_num: safeParseInt(g.stageNum),
      round_num: safeParseInt(g.roundNum),
      round_name: g.roundName || null,
      competition_display_name: g.competitionDisplayName || null,
      start_time: parseDate(g.startTime),
      status_group: safeParseInt(g.statusGroup),
      status_text: g.statusText || null,
      short_status_text: g.shortStatusText || null,
      game_time_display: g.gameTimeDisplay || null,
      just_ended: g.justEnded || false,
      game_time: safeParseFloat(g.gameTime),
      show_countdown: g.showCountdown || false,
      has_lineups: g.hasLineups || false,
      has_missing_players: g.hasMissingPlayers || false,
      has_field_positions: g.hasFieldPositions || false,
      has_tv_networks: g.hasTvNetworks || false,
      has_live_streaming: g.hasLiveStreaming || false,
      has_stats: g.hasStats || false,
      has_standings: g.hasStandings || false,
      has_brackets: g.hasBrackets || false,
      has_previous_meetings: g.hasPreviousMeetings || false,
      has_recent_matches: g.hasRecentMatches || false,
      has_bets: g.hasBets || false,
      has_player_bets: g.hasPlayerBets || false,
      has_news: g.hasNews || false,
      has_video: g.hasVideo || false,
      venue_id: safeParseInt(g.venue?.id),
      is_home_away_inverted: g.isHomeAwayInverted || false,
      winner: safeParseInt(g.winner) || 0,
      home_away_team_order: safeParseInt(g.homeAwayTeamOrder) || 0
    }));

    const { error, count } = await supabase
      .from('matches')
      .upsert(toUpsert, { onConflict: 'api_match_id' });

    if (error) throw new Error(`Error upserting matches: ${error.message}`);

    console.log(`✅ Upserted ${toUpsert.length} matches successfully.`);
    return { success: true, count: toUpsert.length };
  } catch (error) {
    console.error(`❌ Failed to upsert matches:`, error.message);
    throw error;
  }
}
// --- Enhanced Main Edge Function Handler ---
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();
  let results = {
    countries: { success: false, count: 0 },
    leagues: { success: false, count: 0 },
    teams: { success: false, count: 0 },
    matches: { success: false, count: 0 }
  };

  try {
    console.log('🚀 Starting enhanced football data fetch and insertion process...');

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Fetch data from API
    console.log('📡 Fetching data from 365scores API...');
    const response = await fetch(API_URL);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ Successfully fetched API data');
    console.log(`📊 Data summary: ${data.countries?.length || 0} countries, ${data.competitions?.length || 0} leagues, ${data.games?.length || 0} games`);

    // Stage 1: Upsert foundational data (countries and leagues) in parallel
    console.log('\n🏗️ Stage 1: Upserting countries and leagues...');
    const [countriesResult, leaguesResult] = await Promise.all([
      upsertCountries(supabase, data.countries || []),
      upsertLeagues(supabase, data.competitions || [])
    ]);

    results.countries = countriesResult;
    results.leagues = leaguesResult;

    // Stage 2: Upsert teams (depends on countries)
    console.log('\n🏟️ Stage 2: Upserting teams...');
    const teamsResult = await upsertTeams(supabase, data.games || []);
    results.teams = teamsResult;

    // Stage 3: Upsert matches (depends on leagues and teams)
    console.log('\n⚽ Stage 3: Upserting matches...');
    const matchesResult = await upsertMatches(supabase, data.games || []);
    results.matches = matchesResult;

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    const successMessage = `🎉 Database population completed successfully in ${duration}s!
📈 Results Summary:
  • Countries: ${results.countries.count} upserted
  • Leagues: ${results.leagues.count} upserted
  • Teams: ${results.teams.count} upserted
  • Matches: ${results.matches.count} upserted`;

    console.log(successMessage);

    return new Response(JSON.stringify({
      success: true,
      message: "Database population successful!",
      duration: `${duration}s`,
      results: results,
      timestamp: new Date().toISOString()
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.error(`❌ Critical error occurred after ${duration}s:`, error.message);
    console.error('📊 Partial results before failure:', results);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      partialResults: results,
      timestamp: new Date().toISOString()
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
