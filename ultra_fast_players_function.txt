// Ultra-Fast Players Function - Maximum Speed Processing
// Processes all teams in large parallel batches with minimal delays

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Ultra-minimal delay
const delay = (ms) => new Promise((res) => setTimeout(res, ms));

function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

function parseDate(dateString) {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
  } catch {
    return null;
  }
}

// Fetch and process all players for multiple teams in one go
async function fetchAllPlayersData(teams) {
  console.log(`🚀 Fetching player data for ${teams.length} teams in parallel...`);
  
  const fetchPromises = teams.map(async (team) => {
    try {
      const SQUAD_API_URL = `https://webws.365scores.com/web/squads/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitors=${team.api_team_id}`;
      
      const response = await fetch(SQUAD_API_URL);
      if (!response.ok) {
        throw new Error(`API ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.squads || data.squads.length === 0 || !data.squads[0].athletes) {
        return { teamId: team.api_team_id, teamName: team.team_name, players: [], success: true };
      }

      const players = data.squads[0].athletes;
      
      // Filter actual players
      const actualPlayers = players.filter((p) => {
        return p.formationPosition && 
               p.formationPosition.name !== 'Coach' && 
               p.formationPosition.name !== 'Assistant Coach' &&
               p.position && 
               p.position.name !== 'Management' &&
               !p.position.isStaff;
      });

      return { 
        teamId: team.api_team_id, 
        teamName: team.team_name, 
        players: actualPlayers, 
        success: true 
      };

    } catch (error) {
      return { 
        teamId: team.api_team_id, 
        teamName: team.team_name, 
        players: [], 
        success: false, 
        error: error.message 
      };
    }
  });

  const results = await Promise.allSettled(fetchPromises);
  
  return results.map((result, index) => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      const team = teams[index];
      return {
        teamId: team.api_team_id,
        teamName: team.team_name,
        players: [],
        success: false,
        error: result.reason?.message || 'Fetch failed'
      };
    }
  });
}

// Process all players data into database format
function processAllPlayersData(teamsData) {
  console.log(`🔄 Processing player data for database insertion...`);
  
  const allPlayers = [];
  const teamResults = [];

  teamsData.forEach(teamData => {
    if (!teamData.success || teamData.players.length === 0) {
      teamResults.push({
        teamId: teamData.teamId,
        teamName: teamData.teamName,
        success: teamData.success,
        count: 0,
        error: teamData.error || 'No players'
      });
      return;
    }

    const playersToUpsert = teamData.players.map((player) => ({
      api_player_id: player.id,
      player_name: player.name,
      team_id: teamData.teamId, // Use api_team_id as per foreign key constraint
      position: player.position?.name || null,
      number: safeParseInt(player.jerseyNum),
      nationality: null,
      image_url: null,
      cover_image_url: null,
      bio: null,
      age: safeParseInt(player.age),
      jersey_num: safeParseInt(player.jerseyNum),
      birthdate: parseDate(player.birthdate),
      gender: safeParseInt(player.gender),
      height: safeParseInt(player.height),
      short_name: player.shortName || null,
      name_for_url: player.nameForURL || null,
      sport_id: safeParseInt(player.sportId),
      club_id: safeParseInt(player.clubId),
      nationality_id: safeParseInt(player.nationalityId),
      national_team_id: safeParseInt(player.nationalTeamId),
      image_version: safeParseInt(player.imageVersion),
      formation_position_id: safeParseInt(player.formationPosition?.id),
      status: null
    }));

    allPlayers.push(...playersToUpsert);
    teamResults.push({
      teamId: teamData.teamId,
      teamName: teamData.teamName,
      success: true,
      count: playersToUpsert.length
    });
  });

  return { allPlayers, teamResults };
}

// Insert all players in large batches
async function insertAllPlayers(supabase, allPlayers) {
  console.log(`💾 Inserting ${allPlayers.length} players in batches...`);
  
  const BATCH_SIZE = 1000; // Large batch size for maximum speed
  const batches = [];
  
  for (let i = 0; i < allPlayers.length; i += BATCH_SIZE) {
    batches.push(allPlayers.slice(i, i + BATCH_SIZE));
  }

  console.log(`📦 Created ${batches.length} database batches of up to ${BATCH_SIZE} players each`);

  let totalInserted = 0;
  const errors = [];

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`💾 Inserting batch ${i + 1}/${batches.length} (${batch.length} players)...`);
    
    try {
      const { error, count } = await supabase
        .from('players')
        .upsert(batch, { onConflict: 'api_player_id' });

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      totalInserted += batch.length;
      console.log(`   ✅ Batch ${i + 1} completed: ${batch.length} players`);
      
    } catch (error) {
      console.error(`   ❌ Batch ${i + 1} failed: ${error.message}`);
      errors.push({ batch: i + 1, error: error.message, count: batch.length });
    }

    // Minimal delay between database batches
    if (i < batches.length - 1) {
      await delay(50); // Very short delay
    }
  }

  return { totalInserted, errors };
}

// Main handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();

  try {
    console.log('⚡ Starting ULTRA-FAST players insertion...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '', 
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get query parameters
    const url = new URL(req.url);
    const maxTeams = parseInt(url.searchParams.get('maxTeams') || '0'); // 0 = all teams

    // Get all teams with squads
    let teamsQuery = supabase
      .from('teams')
      .select('api_team_id, team_name, has_squad')
      .not('api_team_id', 'is', null)
      .eq('has_squad', true);

    if (maxTeams > 0) {
      teamsQuery = teamsQuery.limit(maxTeams);
    }

    const { data: teams, error: teamsError } = await teamsQuery;

    if (teamsError) {
      throw new Error(`Failed to fetch teams: ${teamsError.message}`);
    }

    if (!teams || teams.length === 0) {
      return new Response(JSON.stringify({
        success: true,
        message: 'No teams found with squads',
        summary: { teamsTotal: 0, totalPlayersInserted: 0 }
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    console.log(`🎯 Processing ${teams.length} teams with ULTRA-FAST method...`);

    // Step 1: Fetch all player data in parallel
    const fetchStartTime = Date.now();
    const teamsData = await fetchAllPlayersData(teams);
    const fetchDuration = (Date.now() - fetchStartTime) / 1000;
    console.log(`✅ Data fetching completed in ${fetchDuration}s`);

    // Step 2: Process all data
    const processStartTime = Date.now();
    const { allPlayers, teamResults } = processAllPlayersData(teamsData);
    const processDuration = (Date.now() - processStartTime) / 1000;
    console.log(`✅ Data processing completed in ${processDuration}s`);

    // Step 3: Insert all players in large batches
    const insertStartTime = Date.now();
    const { totalInserted, errors } = await insertAllPlayers(supabase, allPlayers);
    const insertDuration = (Date.now() - insertStartTime) / 1000;
    console.log(`✅ Database insertion completed in ${insertDuration}s`);

    const endTime = Date.now();
    const totalDuration = (endTime - startTime) / 1000;

    const successfulTeams = teamResults.filter(t => t.success && t.count > 0).length;
    const failedTeams = teamResults.filter(t => !t.success).length;

    const summary = {
      teamsTotal: teams.length,
      teamsWithPlayers: successfulTeams,
      teamsFailed: failedTeams,
      totalPlayersInserted: totalInserted,
      totalDuration: `${totalDuration}s`,
      fetchDuration: `${fetchDuration}s`,
      processDuration: `${processDuration}s`,
      insertDuration: `${insertDuration}s`,
      processingSpeed: `${Math.round(teams.length / totalDuration)} teams/second`,
      insertionSpeed: `${Math.round(totalInserted / insertDuration)} players/second`
    };

    console.log(`\n🎉 ULTRA-FAST processing completed in ${totalDuration}s!`);
    console.log(`📊 Results: ${successfulTeams}/${teams.length} teams with players`);
    console.log(`👥 Total players inserted: ${totalInserted}`);
    console.log(`⚡ Speed: ${summary.processingSpeed}, ${summary.insertionSpeed}`);

    return new Response(JSON.stringify({
      success: true,
      message: 'Ultra-fast players insertion completed successfully',
      summary,
      teamResults: teamResults.slice(0, 50), // Limit results to avoid response size issues
      errors: errors,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.error(`❌ Ultra-fast processing failed after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
