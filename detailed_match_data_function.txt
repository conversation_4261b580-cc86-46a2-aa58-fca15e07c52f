// Detailed Match Data Function - Lineups, Live Stats, and Match Events
// Fetches comprehensive match details for specific matches

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Helper functions
function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

function safeParseFloat(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? null : parsed;
}

// Fetch detailed match information
async function fetchMatchDetails(matchId) {
  const url = `https://webws.365scores.com/web/game/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&gameId=${matchId}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.game || null;
  } catch (error) {
    console.error(`❌ Failed to fetch match ${matchId} details:`, error.message);
    return null;
  }
}

// Fetch match statistics
async function fetchMatchStats(matchId) {
  const url = `https://webws.365scores.com/web/game/stats/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&games=${matchId}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.stats || null;
  } catch (error) {
    console.error(`❌ Failed to fetch match ${matchId} stats:`, error.message);
    return null;
  }
}

// Update detailed match information
async function updateMatchDetails(supabase, matchId, matchDetails) {
  if (!matchDetails) return { success: false, error: 'No match details' };

  try {
    const updateData = {
      minute: safeParseInt(matchDetails.gameTime) > 0 ? safeParseInt(matchDetails.gameTime) : null,
      home_score: matchDetails.homeCompetitor?.score >= 0 ? safeParseInt(matchDetails.homeCompetitor.score) : null,
      away_score: matchDetails.awayCompetitor?.score >= 0 ? safeParseInt(matchDetails.awayCompetitor.score) : null,
      status_text: matchDetails.statusText || null,
      short_status_text: matchDetails.shortStatusText || null,
      game_time_display: matchDetails.gameTimeDisplay || null,
      just_ended: matchDetails.justEnded || false,
      game_time: safeParseFloat(matchDetails.gameTime),
      show_countdown: matchDetails.showCountdown || false,
      has_lineups: matchDetails.hasLineups || false,
      has_stats: matchDetails.hasStats || false,
      winner: safeParseInt(matchDetails.winner) || 0
    };

    const { error } = await supabase
      .from('matches')
      .update(updateData)
      .eq('api_match_id', matchId);

    if (error) {
      console.error(`❌ Database error updating match ${matchId}:`, error.message);
      return { success: false, error: error.message };
    }

    console.log(`✅ Updated match ${matchId} details`);
    return { success: true };

  } catch (error) {
    console.error(`❌ Failed to update match ${matchId} details:`, error.message);
    return { success: false, error: error.message };
  }
}

// Update match top performers with detailed stats
async function updateTopPerformersWithStats(supabase, matchId, topPerformers) {
  if (!topPerformers || !topPerformers.categories) return { success: true, count: 0 };

  try {
    // Delete existing performers for this match
    const { error: deleteError } = await supabase
      .from('match_top_performers')
      .delete()
      .eq('match_id', matchId);

    if (deleteError) {
      console.error(`❌ Error deleting existing performers for match ${matchId}:`, deleteError.message);
    }

    const performersToInsert = [];
    const statsToInsert = [];

    topPerformers.categories.forEach(category => {
      // Process home player
      if (category.homePlayer) {
        const performerId = `${matchId}_${category.homePlayer.id}_home`;
        
        performersToInsert.push({
          match_id: matchId,
          player_id: category.homePlayer.id,
          team_side: 'home',
          category: category.name,
          player_name: category.homePlayer.name,
          position_name: category.homePlayer.positionName || null,
          short_name: category.homePlayer.shortName || null,
          image_version: safeParseInt(category.homePlayer.imageVersion)
        });

        // Process player stats
        if (category.homePlayer.stats) {
          category.homePlayer.stats.forEach(stat => {
            statsToInsert.push({
              performer_id: performerId,
              stat_type: safeParseInt(stat.type),
              stat_name: stat.name,
              stat_value: stat.value
            });
          });
        }
      }

      // Process away player
      if (category.awayPlayer) {
        const performerId = `${matchId}_${category.awayPlayer.id}_away`;
        
        performersToInsert.push({
          match_id: matchId,
          player_id: category.awayPlayer.id,
          team_side: 'away',
          category: category.name,
          player_name: category.awayPlayer.name,
          position_name: category.awayPlayer.positionName || null,
          short_name: category.awayPlayer.shortName || null,
          image_version: safeParseInt(category.awayPlayer.imageVersion)
        });

        // Process player stats
        if (category.awayPlayer.stats) {
          category.awayPlayer.stats.forEach(stat => {
            statsToInsert.push({
              performer_id: performerId,
              stat_type: safeParseInt(stat.type),
              stat_name: stat.name,
              stat_value: stat.value
            });
          });
        }
      }
    });

    // Insert performers
    if (performersToInsert.length > 0) {
      const { error: performersError } = await supabase
        .from('match_top_performers')
        .insert(performersToInsert);

      if (performersError) {
        console.error(`❌ Database error inserting performers for match ${matchId}:`, performersError.message);
        return { success: false, error: performersError.message, count: 0 };
      }
    }

    // Insert performer stats
    if (statsToInsert.length > 0) {
      const { error: statsError } = await supabase
        .from('performer_stats')
        .insert(statsToInsert);

      if (statsError) {
        console.error(`❌ Database error inserting performer stats for match ${matchId}:`, statsError.message);
        // Don't fail the whole operation for stats errors
      }
    }

    console.log(`✅ Updated ${performersToInsert.length} top performers with ${statsToInsert.length} stats for match ${matchId}`);
    return { success: true, count: performersToInsert.length, statsCount: statsToInsert.length };

  } catch (error) {
    console.error(`❌ Failed to update top performers for match ${matchId}:`, error.message);
    return { success: false, error: error.message, count: 0 };
  }
}

// Get active/recent matches that need detailed updates
async function getMatchesToUpdate(supabase, limit = 50) {
  try {
    // Get matches that are live, recently finished, or upcoming in next 24 hours
    const { data: matches, error } = await supabase
      .from('matches')
      .select('api_match_id, status_group, start_time, status_text')
      .or('status_group.eq.1,status_group.eq.2,status_group.eq.3') // Live, scheduled, or finished
      .gte('start_time', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
      .lte('start_time', new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()) // Next 24 hours
      .order('start_time', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('❌ Error fetching matches to update:', error.message);
      return [];
    }

    return matches || [];
  } catch (error) {
    console.error('❌ Failed to get matches to update:', error.message);
    return [];
  }
}

// Main handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();
  const results = {
    matchesUpdated: 0,
    performersUpdated: 0,
    statsUpdated: 0,
    errors: []
  };

  try {
    console.log('🔍 Starting detailed match data update...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '', 
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get query parameters
    const url = new URL(req.url);
    const specificMatchId = url.searchParams.get('matchId'); // Update specific match
    const maxMatches = parseInt(url.searchParams.get('maxMatches') || '20'); // Limit for performance
    const updateStats = url.searchParams.get('stats') !== 'false'; // Default: true

    let matchesToUpdate = [];

    if (specificMatchId) {
      // Update specific match
      matchesToUpdate = [{ api_match_id: parseInt(specificMatchId) }];
      console.log(`🎯 Updating specific match: ${specificMatchId}`);
    } else {
      // Get active matches to update
      matchesToUpdate = await getMatchesToUpdate(supabase, maxMatches);
      console.log(`📊 Found ${matchesToUpdate.length} matches to update`);
    }

    if (matchesToUpdate.length === 0) {
      return new Response(JSON.stringify({
        success: true,
        message: 'No matches found to update',
        results,
        timestamp: new Date().toISOString()
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    // Process each match
    for (const match of matchesToUpdate) {
      const matchId = match.api_match_id;
      
      try {
        console.log(`🔄 Processing match ${matchId}...`);

        // Fetch detailed match data
        const matchDetails = await fetchMatchDetails(matchId);
        
        if (matchDetails) {
          // Update basic match details
          const updateResult = await updateMatchDetails(supabase, matchId, matchDetails);
          
          if (updateResult.success) {
            results.matchesUpdated++;
          } else {
            results.errors.push(`Match ${matchId}: ${updateResult.error}`);
          }

          // Update top performers with stats
          if (updateStats && matchDetails.topPerformers) {
            const performersResult = await updateTopPerformersWithStats(supabase, matchId, matchDetails.topPerformers);
            
            if (performersResult.success) {
              results.performersUpdated += performersResult.count;
              results.statsUpdated += performersResult.statsCount || 0;
            } else {
              results.errors.push(`Performers ${matchId}: ${performersResult.error}`);
            }
          }
        } else {
          results.errors.push(`Match ${matchId}: No details available`);
        }

        // Respectful delay between requests
        await new Promise(resolve => setTimeout(resolve, 200));

      } catch (error) {
        console.error(`❌ Error processing match ${matchId}:`, error.message);
        results.errors.push(`Match ${matchId}: ${error.message}`);
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`🎉 Detailed match update completed in ${duration}s!`);
    console.log(`📊 Updated: ${results.matchesUpdated} matches, ${results.performersUpdated} performers, ${results.statsUpdated} stats`);

    return new Response(JSON.stringify({
      success: true,
      message: 'Detailed match data update completed',
      duration: `${duration}s`,
      results,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.error(`❌ Detailed match update failed after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      partialResults: results,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
