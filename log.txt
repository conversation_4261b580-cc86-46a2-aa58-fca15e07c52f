[{"event_message": "   ✅ SC Sderot: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "b7a91ac5-d706-41e3-a3e3-f37d25e54e72", "level": "info", "timestamp": 1758082872039000}, {"event_message": "   ✅ <PERSON> <PERSON><PERSON>: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "9db5e532-e934-44de-b86d-91c3c85fa4f6", "level": "info", "timestamp": 1758082871835000}, {"event_message": "   ❌ Power Dynamos: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "154b1ede-c1c1-4d78-814d-cefb345a61a4", "level": "error", "timestamp": 1758082871631000}, {"event_message": "   ✅ Nchanga Rangers: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "69a84ad0-6d07-4b26-bea2-ffe7ce197b99", "level": "info", "timestamp": 1758082871255000}, {"event_message": "   ❌ Red Arrows: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "cff19ea5-58c7-41e3-9d72-7514917694c4", "level": "error", "timestamp": 1758082870953000}, {"event_message": "   ❌ Zesco United: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "46ca22ab-bb4e-4840-b3af-f40103ca7a3a", "level": "error", "timestamp": 1758082870575000}, {"event_message": "   ❌ Dr<PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "4ed3e4a5-3750-491a-931e-7c876ba09e5d", "level": "error", "timestamp": 1758082870188000}, {"event_message": "\n🏟️ Processing batch 23/65 (10 teams)\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "fe48c643-8cf3-4988-8731-6f0f84711a17", "level": "info", "timestamp": 1758082870009000}, {"event_message": "⏳ Waiting between batches...\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "6c787e94-7ab5-4e46-914e-c0afd4cc8e0b", "level": "info", "timestamp": 1758082869009000}, {"event_message": "   ❌ Drita: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "07951fea-bfc3-4823-87b7-b129caf5f529", "level": "error", "timestamp": 1758082868807000}, {"event_message": "   ❌ Atalanta U19: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "365dd3c3-d3cf-440f-8efd-9cc7adcd011a", "level": "error", "timestamp": 1758082868426000}, {"event_message": "   ❌ PSG U19: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "35139563-3c78-437b-bf97-b24a0f884759", "level": "error", "timestamp": 1758082868048000}, {"event_message": "   ❌ <PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "15494be5-0dfb-4abb-96fc-10e0af4b8e81", "level": "error", "timestamp": 1758082867667000}, {"event_message": "   ❌ <PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "af0a45d8-a8cd-4cb7-aa51-ba363355b212", "level": "error", "timestamp": 1758082867289000}, {"event_message": "   ❌ <PERSON><PERSON><PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "abd3db1a-4c6f-4ec4-b93e-a202a65f48d1", "level": "error", "timestamp": 1758082866890000}, {"event_message": "   ✅ Polissya Stavky: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "6ae53b28-78fe-4832-9eb1-730a88dfeb19", "level": "info", "timestamp": 1758082866501000}, {"event_message": "   ❌ <PERSON><PERSON><PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "645e9566-bbe8-4be5-b006-e64f228166a1", "level": "error", "timestamp": 1758082866196000}, {"event_message": "   ❌ Dubrava: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "5c51c177-e60a-48fa-97d3-1a300552477a", "level": "error", "timestamp": 1758082865819000}, {"event_message": "   ❌ Ratchaburi FC: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "5cdebffa-f6e0-4966-bc2b-99d92090c2cb", "level": "error", "timestamp": 1758082865448000}, {"event_message": "\n🏟️ Processing batch 22/65 (10 teams)\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "b3ae3926-7030-4302-9268-9592e5fd7e75", "level": "info", "timestamp": 1758082865244000}, {"event_message": "⏳ Waiting between batches...\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "2e9ee617-806f-4421-bc87-e67bbb087e65", "level": "info", "timestamp": 1758082864244000}, {"event_message": "   ❌ <PERSON> Din<PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "1a2dd25a-327f-49bb-9951-4cdfb4c5d4af", "level": "error", "timestamp": 1758082864042000}, {"event_message": "   ❌ Vissel Kobe: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "67852bb3-58b5-4d31-b04b-53227b28c48b", "level": "error", "timestamp": 1758082863661000}, {"event_message": "   ❌ Al-Arabi: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "31a19468-0aab-4c6d-ba0c-e9565d6ee032", "level": "error", "timestamp": 1758082863273000}, {"event_message": "   ❌ Al Faysaly: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "2ec8c446-bb1f-47fe-b334-5cb0c2d5132f", "level": "error", "timestamp": 1758082862985000}, {"event_message": "   ❌ Dynamo Makhachkala: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "b47006a9-1bbb-4493-a9c4-68c0c40477d9", "level": "error", "timestamp": 1758082862701000}, {"event_message": "   ❌ Valletta FC: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "d82179c6-112f-40a1-a22f-72a1a128279b", "level": "error", "timestamp": 1758082862415000}, {"event_message": "   ❌ Floriana FC: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "8b25f19b-f386-42bf-a2e6-227d112cdd8e", "level": "error", "timestamp": 1758082862029000}, {"event_message": "   ❌ Hamrun Spartans: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "6114bca2-a680-4660-a323-8d40a4ddd49b", "level": "error", "timestamp": 1758082861642000}, {"event_message": "   ✅ St. Patrick: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "864fa05e-5b84-481b-b90c-734f2f8f499b", "level": "info", "timestamp": 1758082861252000}, {"event_message": "   ✅ Randers FC: 31 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "ef69af6a-960b-4355-9d86-1ffb229399f2", "level": "info", "timestamp": 1758082860936000}, {"event_message": "\n🏟️ Processing batch 21/65 (10 teams)\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "6a9be5db-545c-487b-b1d5-9cfa75c798ce", "level": "info", "timestamp": 1758082860736000}, {"event_message": "⏳ Waiting between batches...\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "a022abde-8c07-4416-a08a-532a633166e3", "level": "info", "timestamp": 1758082859735000}, {"event_message": "   ✅ <PERSON><PERSON>: 2 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "2b5fb3c2-744f-4576-a261-560f9b82ca11", "level": "info", "timestamp": 1758082859533000}, {"event_message": "   ✅ <PERSON><PERSON><PERSON><PERSON><PERSON>: 32 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "36e61912-ce07-49b4-b540-fb42b3a3e58d", "level": "info", "timestamp": 1758082859150000}, {"event_message": "   ❌ Hvidovre: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "89184b7a-d227-4961-b870-aa3ed7e7fb69", "level": "error", "timestamp": 1758082858757000}, {"event_message": "   ❌ Abha Club: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "1d806ee2-5e81-4c09-9c63-91423bcae766", "level": "error", "timestamp": 1758082858375000}, {"event_message": "   ❌ Shanghai Port: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "26074f1e-8205-4f7a-b0a6-f7b0eac789f4", "level": "error", "timestamp": 1758082858077000}, {"event_message": "   ❌ Belediye Derincespor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "64dc33b1-72eb-41b8-9418-95d923ab0804", "level": "error", "timestamp": 1758082857693000}, {"event_message": "   ❌ Karabukspor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "48578f94-7cd3-4e6c-96de-49a40e3ca546", "level": "error", "timestamp": 1758082857321000}, {"event_message": "   ❌ Beyoglu Yeni Carsi Fk: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "1d7b948a-db6f-45ad-b825-413302434a5d", "level": "error", "timestamp": 1758082856927000}, {"event_message": "   ❌ Silivrispor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "a35030f6-1a4d-45d1-a95d-6aee7034e224", "level": "error", "timestamp": 1758082856547000}, {"event_message": "   ❌ Altınordu SK: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "22f9a49a-1c61-4e92-90f6-e19c301930c0", "level": "error", "timestamp": 1758082856158000}, {"event_message": "\n🏟️ Processing batch 20/65 (10 teams)\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "9b642b55-5876-4fb6-a405-9568f0ec9b19", "level": "info", "timestamp": 1758082855982000}, {"event_message": "⏳ Waiting between batches...\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "d262219a-036e-4842-a4fb-c67454e9400b", "level": "info", "timestamp": 1758082854981000}, {"event_message": "   ❌ Kütahyaspor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "0fd1963d-32c2-4281-83ce-1c5f575578a7", "level": "error", "timestamp": 1758082854780000}, {"event_message": "   ❌ Arsenal: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "28f4fb2a-6331-4dc1-a7a5-c09ee89d571a", "level": "error", "timestamp": 1758082854390000}, {"event_message": "   ❌ Is<PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "71ea5325-1eb2-4fdf-b946-0db152ac50b4", "level": "error", "timestamp": 1758082854113000}, {"event_message": "   ✅ Bergama Belediyespor: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "e1ebdc8b-4a5c-4ba6-8012-3a7eba80b744", "level": "info", "timestamp": 1758082853833000}, {"event_message": "   ❌ Tractor FC: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "5e68a1f1-7f63-40f9-b291-9f76b96f9501", "level": "error", "timestamp": 1758082853630000}, {"event_message": "   ❌ <PERSON><PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "682ea1a5-b1d5-4cca-82f1-e3967d3b42c3", "level": "error", "timestamp": 1758082853347000}, {"event_message": "   ❌ <PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "7cd9b332-62c0-428a-98c6-d55530d4c1fa", "level": "error", "timestamp": 1758082853065000}, {"event_message": "   ❌ Al Baten: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "4e096219-a927-4ab6-b3fe-3af574feb844", "level": "error", "timestamp": 1758082852786000}, {"event_message": "   ❌ <PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "0b0468ad-451b-4758-a8ad-20a4defbce80", "level": "error", "timestamp": 1758082852507000}, {"event_message": "   ❌ Lokomotiv Moscow: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "79b1499e-69ac-450f-8e09-cbf816a30cb1", "level": "error", "timestamp": 1758082852224000}, {"event_message": "\n🏟️ Processing batch 19/65 (10 teams)\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "d7a8e89e-cd90-4d12-87c6-8318c52d96fc", "level": "info", "timestamp": 1758082852148000}, {"event_message": "⏳ Waiting between batches...\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "aeec7fd9-3308-411e-8fcc-94aa12c39b57", "level": "info", "timestamp": 1758082851146000}, {"event_message": "   ❌ Akron Tolyatti: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "ee8cbf11-1436-4189-be72-96c4ed32b1e5", "level": "error", "timestamp": 1758082850946000}, {"event_message": "   ❌ FK Panevezys: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "1df7f70d-b9e0-48e8-b431-26cafb2d1022", "level": "error", "timestamp": 1758082850663000}, {"event_message": "   ❌ Suduva: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "b83df113-04ca-46ac-af39-6c10a3ebc34c", "level": "error", "timestamp": 1758082850374000}, {"event_message": "   ❌ KabuSCorp SC: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "35bd1ac2-ce64-4efb-8010-4f1423c4657f", "level": "error", "timestamp": 1758082850094000}, {"event_message": "   ❌ 1461 Trabzon: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "52de9ad9-4d4b-4106-af7a-14652dc5a5cc", "level": "error", "timestamp": 1758082849815000}, {"event_message": "   ✅ Amasyaspor 1968 FK: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "a7902785-5608-49b6-b30d-bf5ea1a89db1", "level": "info", "timestamp": 1758082849433000}, {"event_message": "   ❌ Fatsa Belediyespor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "b79e4e1b-8bad-41dd-8599-8cdc0af64dc2", "level": "error", "timestamp": 1758082849130000}, {"event_message": "   ❌ Kastamonuspor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "c19859d8-04f5-413d-84ae-8489a2907882", "level": "error", "timestamp": 1758082848737000}, {"event_message": "   ✅ Cankaya Fk: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "4387d6aa-4d48-4dd3-ab70-4e3dd73543fd", "level": "info", "timestamp": 1758082848337000}, {"event_message": "   ✅ Somaspor: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "8eb00fb4-dcd0-4f8e-b1f5-bf501d52fc57", "level": "info", "timestamp": 1758082848035000}, {"event_message": "\n🏟️ Processing batch 18/65 (10 teams)\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "80a20551-db50-4e57-88e9-fa1431fe2697", "level": "info", "timestamp": 1758082847924000}, {"event_message": "⏳ Waiting between batches...\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "da5cc25c-f5bf-42ba-ab19-0343aced17b8", "level": "info", "timestamp": 1758082846923000}, {"event_message": "   ❌ Pazarspor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "fc3776f2-a4f0-4458-a1e6-db4381d6192d", "level": "error", "timestamp": 1758082846722000}, {"event_message": "   ❌ Erbaaspor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "152ccf18-6bee-468f-a885-91517f0e8b55", "level": "error", "timestamp": 1758082846337000}, {"event_message": "   ✅ Sardarapat FC: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "fca6078b-1451-45ea-9013-cf25ec843911", "level": "info", "timestamp": 1758082845942000}, {"event_message": "   ❌ Mika: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "9f971e44-6553-4ad7-ab76-bb576ac5adf3", "level": "error", "timestamp": 1758082845630000}, {"event_message": "   ❌ Koge (W): Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "50af9636-9d0e-4f76-8fb7-bb8a1c99fbfb", "level": "error", "timestamp": 1758082845252000}, {"event_message": "   ✅ <PERSON><PERSON> (W): 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "b7c8e055-d1b7-43c4-b208-162a80a25287", "level": "info", "timestamp": 1758082844849000}, {"event_message": "   ❌ Aris Salonica: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "789a8be9-40f3-4007-b7ae-04ba71b6dfb4", "level": "error", "timestamp": 1758082844544000}, {"event_message": "   ❌ Panaitolikos: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "123001dd-b0d9-42b2-80c3-1c3622299731", "level": "error", "timestamp": 1758082844161000}, {"event_message": "   ❌ Asteras Tripolis: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "176dc734-6bce-432b-9947-cc0875b2c772", "level": "error", "timestamp": 1758082843780000}, {"event_message": "   ❌ Kifisia: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "c5f03446-cb19-48a0-a259-5567844b3428", "level": "error", "timestamp": 1758082843372000}, {"event_message": "\n🏟️ Processing batch 17/65 (10 teams)\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "c9c533aa-2cda-402b-85e6-8f982d1a5a4b", "level": "info", "timestamp": 1758082843186000}, {"event_message": "⏳ Waiting between batches...\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "2c53179b-bf58-4286-a6fe-8bb4335c6e42", "level": "info", "timestamp": 1758082842185000}, {"event_message": "   ✅ Nebitci: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "04bb3804-7559-4eb2-a863-f5b11c71d365", "level": "info", "timestamp": 1758082841983000}, {"event_message": "   ✅ Kopetdag Asgabat: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "3621f1ea-a645-46ba-9b46-b5a7b4d2ba64", "level": "info", "timestamp": 1758082841681000}, {"event_message": "   ✅ Mönchengladbach II: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "1a968147-9f7a-4e95-af89-d04390013d44", "level": "info", "timestamp": 1758082841378000}, {"event_message": "   ❌ Koln II: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "e16f8ed0-018e-4450-b1f2-1615c3b688fe", "level": "error", "timestamp": 1758082841074000}, {"event_message": "   ❌ <PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "53e0129d-8107-4e67-8c4c-8860e924d864", "level": "error", "timestamp": 1758082840684000}, {"event_message": "   ❌ Kahramanmarasspor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "7c96c58f-00b1-4476-8484-bfa972a3dfa7", "level": "error", "timestamp": 1758082840302000}, {"event_message": "   ✅ JS Kairouan: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "85f5dbd4-54b2-4234-bde7-a5d08005ae2a", "level": "info", "timestamp": 1758082839914000}, {"event_message": "   ❌ <PERSON><PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "a52f3826-e34f-46ac-bb3e-c00271ad7cdf", "level": "error", "timestamp": 1758082839709000}, {"event_message": "   ❌ Interclube Luanda: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "19ae6f12-7321-432e-aa34-b70757da5bd3", "level": "error", "timestamp": 1758082839423000}, {"event_message": "   ❌ Us Biskra: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "e276bece-ae21-45c6-84e5-e8b705cb220f", "level": "error", "timestamp": 1758082839140000}, {"event_message": "\n🏟️ Processing batch 16/65 (10 teams)\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "c2a3fbcb-88ed-4761-bcbb-7cacd5edea5e", "level": "info", "timestamp": 1758082839058000}, {"event_message": "⏳ Waiting between batches...\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "d6790a19-05df-4d3f-bdd4-e1a73f761c52", "level": "info", "timestamp": 1758082838058000}, {"event_message": "   ✅ <PERSON><PERSON><PERSON>: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "2ec77174-6160-457d-bc5e-15641c72ff7a", "level": "info", "timestamp": 1758082837856000}, {"event_message": "   ✅ Adana: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "b6fd54a4-b913-4fb4-b15f-5c62cc01d9c9", "level": "info", "timestamp": 1758082837653000}, {"event_message": "   ❌ Bodrumspor: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "33f67238-80d0-4ee8-bd9b-996a64630c6b", "level": "error", "timestamp": 1758082837349000}, {"event_message": "   ❌ Tire 2021 Fk: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "b9321aae-5be3-4fdc-b8e8-3073a34f3207", "level": "error", "timestamp": 1758082837065000}, {"event_message": "   ✅ Karakopru: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "2b739d8e-46b1-469f-8ade-104b4ef397ef", "level": "info", "timestamp": 1758082836780000}, {"event_message": "   ❌ <PERSON><PERSON>: Error upserting players: insert or update on table \"players\" violates foreign key constraint \"players_team_id_fkey\"\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "99b8bc3a-fe76-4937-a586-0df0d79ab98e", "level": "error", "timestamp": 1758082836577000}, {"event_message": "   ✅ Kahta: 0 players\n", "event_type": "Log", "function_id": "0dbfbe5c-8001-4885-ad1e-1bb87882439d", "id": "d2922d02-35a3-4ee4-aee2-ffd32500546b", "level": "info", "timestamp": 1758082836283000}]