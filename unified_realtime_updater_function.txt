// Unified Real-Time Football Data Updater Function
// Centralized function that updates ALL database tables in one go
// Updates: matches, league_standings, match_top_performers, performer_stats

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Helper functions
function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

function safeParseFloat(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? null : parsed;
}

function parseDateTime(dateString) {
  if (!dateString) return null;
  try {
    return new Date(dateString).toISOString();
  } catch {
    return null;
  }
}

// Fetch all match data from multiple endpoints
async function fetchAllMatchData() {
  const endpoints = [
    'https://webws.365scores.com/web/games/current/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1',
    'https://webws.365scores.com/web/games/fixtures/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1',
    'https://webws.365scores.com/web/games/results/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1'
  ];

  try {
    console.log('📊 Fetching match data from all endpoints...');
    
    const responses = await Promise.allSettled(
      endpoints.map(url => fetch(url).then(res => res.ok ? res.json() : { games: [] }))
    );

    const allMatches = [];
    responses.forEach((response, index) => {
      if (response.status === 'fulfilled' && response.value.games) {
        allMatches.push(...response.value.games);
      }
    });

    // Deduplicate matches by ID
    const uniqueMatches = allMatches.filter((match, index, self) => 
      index === self.findIndex(m => m.id === match.id)
    );

    console.log(`📈 Found ${uniqueMatches.length} unique matches`);
    return uniqueMatches;

  } catch (error) {
    console.error('❌ Failed to fetch match data:', error.message);
    return [];
  }
}

// Fetch detailed match information for specific matches
async function fetchMatchDetails(matchId) {
  const url = `https://webws.365scores.com/web/game/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&gameId=${matchId}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) return null;
    
    const data = await response.json();
    return data.game || null;
  } catch (error) {
    console.error(`❌ Failed to fetch match ${matchId} details:`, error.message);
    return null;
  }
}

// Fetch standings for competitions
async function fetchStandings(competitionId) {
  const url = `https://webws.365scores.com/web/standings/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitions=${competitionId}&live=false&isPreview=true`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) return [];
    
    const data = await response.json();
    return data.standings || [];
  } catch (error) {
    console.error(`❌ Failed to fetch standings for competition ${competitionId}:`, error.message);
    return [];
  }
}

// UNIFIED UPDATE FUNCTION - Updates all database tables
async function updateAllFootballData(supabase, options = {}) {
  const {
    maxStandingsCompetitions = 8,
    maxTopPerformersMatches = 5,
    updateStandings = true,
    updateTopPerformers = true
  } = options;

  const results = {
    matches: { count: 0, success: true },
    standings: { count: 0, competitions: 0, success: true },
    topPerformers: { count: 0, matches: 0, success: true },
    performerStats: { count: 0, success: true },
    errors: []
  };

  try {
    // Step 1: Fetch all match data
    const matches = await fetchAllMatchData();
    
    if (matches.length === 0) {
      console.log('⚠️ No matches found to update');
      return results;
    }

    // Step 2: Update MATCHES table
    console.log('🏟️ Updating matches table...');
    const matchesToUpsert = matches.map(match => ({
      api_match_id: match.id,
      home_team_id: match.homeCompetitor?.id || null,
      away_team_id: match.awayCompetitor?.id || null,
      competition_id: match.competitionId,
      sport_id: match.sportId,
      season_num: safeParseInt(match.seasonNum),
      stage_num: safeParseInt(match.stageNum),
      round_num: safeParseInt(match.roundNum),
      round_name: match.roundName || null,
      competition_display_name: match.competitionDisplayName || null,
      start_time: parseDateTime(match.startTime),
      match_date: match.startTime ? new Date(match.startTime).toISOString().split('T')[0] : null,
      match_time: match.startTime ? new Date(match.startTime).toISOString().split('T')[1].split('.')[0] : null,
      status_group: safeParseInt(match.statusGroup),
      status_text: match.statusText || null,
      short_status_text: match.shortStatusText || null,
      game_time_display: match.gameTimeDisplay || null,
      just_ended: match.justEnded || false,
      game_time: safeParseFloat(match.gameTime),
      show_countdown: match.showCountdown || false,
      home_score: match.homeCompetitor?.score >= 0 ? safeParseInt(match.homeCompetitor.score) : null,
      away_score: match.awayCompetitor?.score >= 0 ? safeParseInt(match.awayCompetitor.score) : null,
      minute: safeParseFloat(match.gameTime) > 0 ? safeParseInt(match.gameTime) : null,
      has_lineups: match.hasLineups || false,
      has_missing_players: match.hasMissingPlayers || false,
      has_field_positions: match.hasFieldPositions || false,
      has_tv_networks: match.hasTVNetworks || false,
      has_live_streaming: match.hasLiveStreaming || false,
      has_stats: match.hasStats || false,
      has_standings: match.hasStandings || false,
      has_brackets: match.hasBrackets || false,
      has_previous_meetings: match.hasPreviousMeetings || false,
      has_recent_matches: match.hasRecentMatches || false,
      has_bets: match.hasBets || false,
      has_player_bets: match.hasPlayerBets || false,
      has_news: match.hasNews || false,
      has_video: match.hasVideo || false,
      venue_id: match.venue?.id || null,
      is_home_away_inverted: match.isHomeAwayInverted || false,
      winner: safeParseInt(match.winner) || 0,
      home_away_team_order: safeParseInt(match.homeAwayTeamOrder) || 0
    }));

    const { error: matchesError } = await supabase
      .from('matches')
      .upsert(matchesToUpsert, { onConflict: 'api_match_id' });

    if (matchesError) {
      results.errors.push(`Matches: ${matchesError.message}`);
      results.matches.success = false;
    } else {
      results.matches.count = matchesToUpsert.length;
      console.log(`✅ Updated ${matchesToUpsert.length} matches`);
    }

    // Step 3: Update LEAGUE_STANDINGS table
    if (updateStandings) {
      console.log('📊 Updating league standings...');
      
      const activeCompetitions = [...new Set(matches.map(m => m.competitionId))].slice(0, maxStandingsCompetitions);
      
      for (const competitionId of activeCompetitions) {
        try {
          const standingsData = await fetchStandings(competitionId);
          
          if (!standingsData || standingsData.length === 0) continue;

          for (const standing of standingsData) {
            if (!standing.rows || standing.rows.length === 0) continue;

            // Get team mappings for foreign key relationships
            const apiTeamIds = standing.rows.map(row => row.competitor?.id).filter(id => id);
            
            const { data: teamMappings } = await supabase
              .from('teams')
              .select('team_id, api_team_id')
              .in('api_team_id', apiTeamIds);

            const teamIdMap = {};
            teamMappings?.forEach(team => {
              teamIdMap[team.api_team_id] = team.team_id;
            });

            const standingsToUpsert = standing.rows
              .filter(row => row.competitor?.id && teamIdMap[row.competitor.id])
              .map(row => ({
                competition_id: standing.competitionId,
                season_num: standing.seasonNum,
                stage_num: standing.stageNum,
                team_id: teamIdMap[row.competitor.id], // Use internal team_id
                position: safeParseInt(row.position),
                games_played: safeParseInt(row.gamePlayed) || 0,
                games_won: safeParseInt(row.gamesWon) || 0,
                games_lost: safeParseInt(row.gamesLost) || 0,
                games_even: safeParseInt(row.gamesEven) || 0,
                goals_for: safeParseInt(row.for) || 0,
                goals_against: safeParseInt(row.against) || 0,
                goal_ratio: safeParseFloat(row.ratio) || 0.0,
                points: safeParseFloat(row.points) || 0.0,
                strike: safeParseInt(row.strike) || 0,
                games_ot: safeParseInt(row.gamesOT) || 0,
                games_won_on_ot: safeParseInt(row.gamesWonOnOT) || 0,
                games_won_on_pen: safeParseInt(row.gamesWonOnPen) || 0,
                games_loss_on_ot: safeParseInt(row.gamesLossOnOT) || 0,
                games_loss_on_pen: safeParseInt(row.gamesLossOnPen) || 0,
                pct: row.pct || null,
                trend: safeParseInt(row.trend) || 0,
                next_match_id: null, // Can be populated separately if needed
                destination_num: safeParseInt(row.destinationNum),
                has_points_deduction: row.hasPointsDeduction || false,
                ppg: safeParseFloat(row.ppg) || 0.0,
                oppg: safeParseFloat(row.oppg) || 0.0,
                updated_at: new Date().toISOString()
              }));

            if (standingsToUpsert.length > 0) {
              // Delete existing standings for this competition/season/stage
              await supabase
                .from('league_standings')
                .delete()
                .eq('competition_id', standing.competitionId)
                .eq('season_num', standing.seasonNum)
                .eq('stage_num', standing.stageNum);

              // Insert new standings
              const { error: standingsError } = await supabase
                .from('league_standings')
                .insert(standingsToUpsert);

              if (standingsError) {
                results.errors.push(`Standings ${competitionId}: ${standingsError.message}`);
              } else {
                results.standings.count += standingsToUpsert.length;
              }
            }
          }

          results.standings.competitions++;
          
          // Small delay between competitions
          await new Promise(resolve => setTimeout(resolve, 200));

        } catch (error) {
          results.errors.push(`Standings ${competitionId}: ${error.message}`);
        }
      }

      console.log(`✅ Updated ${results.standings.count} standings for ${results.standings.competitions} competitions`);
    }

    // Step 4: Update MATCH_TOP_PERFORMERS and PERFORMER_STATS tables
    if (updateTopPerformers) {
      console.log('🌟 Updating top performers and stats...');
      
      // Focus on live and recent matches
      const importantMatches = matches
        .filter(m => m.statusGroup <= 2) // Live or scheduled matches
        .slice(0, maxTopPerformersMatches);

      for (const match of importantMatches) {
        try {
          const matchDetails = await fetchMatchDetails(match.id);
          
          if (!matchDetails || !matchDetails.topPerformers) continue;

          // Delete existing performers and stats for this match
          const { data: existingPerformers } = await supabase
            .from('match_top_performers')
            .select('performer_id')
            .eq('match_id', match.id);

          if (existingPerformers && existingPerformers.length > 0) {
            // Delete performer stats first (foreign key dependency)
            await supabase
              .from('performer_stats')
              .delete()
              .in('performer_id', existingPerformers.map(p => p.performer_id));
          }

          // Delete performers
          await supabase
            .from('match_top_performers')
            .delete()
            .eq('match_id', match.id);

          const performersToInsert = [];
          const statsToInsert = [];

          matchDetails.topPerformers.categories?.forEach(category => {
            // Process home player
            if (category.homePlayer) {
              const performerId = `${match.id}_${category.homePlayer.id}_home`;
              
              performersToInsert.push({
                performer_id: performerId,
                match_id: match.id,
                player_id: category.homePlayer.id,
                team_side: 'home',
                category: category.name,
                player_name: category.homePlayer.name,
                position_name: category.homePlayer.positionName || null,
                short_name: category.homePlayer.shortName || null,
                image_version: safeParseInt(category.homePlayer.imageVersion)
              });

              // Add player stats
              if (category.homePlayer.stats) {
                category.homePlayer.stats.forEach(stat => {
                  statsToInsert.push({
                    performer_id: performerId,
                    stat_type: safeParseInt(stat.type),
                    stat_name: stat.name,
                    stat_value: stat.value
                  });
                });
              }
            }

            // Process away player
            if (category.awayPlayer) {
              const performerId = `${match.id}_${category.awayPlayer.id}_away`;
              
              performersToInsert.push({
                performer_id: performerId,
                match_id: match.id,
                player_id: category.awayPlayer.id,
                team_side: 'away',
                category: category.name,
                player_name: category.awayPlayer.name,
                position_name: category.awayPlayer.positionName || null,
                short_name: category.awayPlayer.shortName || null,
                image_version: safeParseInt(category.awayPlayer.imageVersion)
              });

              // Add player stats
              if (category.awayPlayer.stats) {
                category.awayPlayer.stats.forEach(stat => {
                  statsToInsert.push({
                    performer_id: performerId,
                    stat_type: safeParseInt(stat.type),
                    stat_name: stat.name,
                    stat_value: stat.value
                  });
                });
              }
            }
          });

          // Insert performers
          if (performersToInsert.length > 0) {
            const { error: performersError } = await supabase
              .from('match_top_performers')
              .insert(performersToInsert);

            if (performersError) {
              results.errors.push(`Performers ${match.id}: ${performersError.message}`);
            } else {
              results.topPerformers.count += performersToInsert.length;
            }
          }

          // Insert performer stats
          if (statsToInsert.length > 0) {
            const { error: statsError } = await supabase
              .from('performer_stats')
              .insert(statsToInsert);

            if (statsError) {
              results.errors.push(`Stats ${match.id}: ${statsError.message}`);
            } else {
              results.performerStats.count += statsToInsert.length;
            }
          }

          results.topPerformers.matches++;
          
          // Small delay between matches
          await new Promise(resolve => setTimeout(resolve, 150));

        } catch (error) {
          results.errors.push(`Match ${match.id}: ${error.message}`);
        }
      }

      console.log(`✅ Updated ${results.topPerformers.count} top performers with ${results.performerStats.count} stats for ${results.topPerformers.matches} matches`);
    }

    return results;

  } catch (error) {
    console.error('❌ Failed to update football data:', error.message);
    results.errors.push(`General error: ${error.message}`);
    return results;
  }
}

// Main handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();

  try {
    console.log('🚀 Starting unified real-time football data update...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '', 
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get query parameters
    const url = new URL(req.url);
    const options = {
      updateStandings: url.searchParams.get('standings') !== 'false',
      updateTopPerformers: url.searchParams.get('topPerformers') !== 'false',
      maxStandingsCompetitions: parseInt(url.searchParams.get('maxStandingsCompetitions') || '8'),
      maxTopPerformersMatches: parseInt(url.searchParams.get('maxTopPerformersMatches') || '5')
    };

    // Execute unified update
    const results = await updateAllFootballData(supabase, options);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`🎉 Unified update completed in ${duration}s!`);
    console.log(`📊 Updated: ${results.matches.count} matches, ${results.standings.count} standings, ${results.topPerformers.count} performers, ${results.performerStats.count} stats`);

    return new Response(JSON.stringify({
      success: true,
      message: 'Unified real-time football data update completed successfully',
      duration: `${duration}s`,
      results: {
        matches: results.matches.count,
        standings: results.standings.count,
        topPerformers: results.topPerformers.count,
        performerStats: results.performerStats.count,
        standingsCompetitions: results.standings.competitions,
        topPerformersMatches: results.topPerformers.matches,
        errors: results.errors
      },
      timestamp: new Date().toISOString(),
      nextUpdateRecommended: new Date(Date.now() + 60000).toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.error(`❌ Unified update failed after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
