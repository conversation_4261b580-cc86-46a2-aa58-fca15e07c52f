// Real-Time Matches Data Updater Function
// Updates matches, standings, lineups, and statistics every minute for near real-time experience

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Helper functions
function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

function safeParseFloat(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? null : parsed;
}

function parseDateTime(dateString) {
  if (!dateString) return null;
  try {
    return new Date(dateString).toISOString();
  } catch {
    return null;
  }
}

function parseDate(dateString) {
  if (!dateString) return null;
  try {
    return new Date(dateString).toISOString().split('T')[0];
  } catch {
    return null;
  }
}

function parseTime(dateString) {
  if (!dateString) return null;
  try {
    return new Date(dateString).toISOString().split('T')[1].split('.')[0];
  } catch {
    return null;
  }
}

// Fetch current/live matches
async function fetchCurrentMatches() {
  const url = 'https://webws.365scores.com/web/games/current/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1';
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.games || [];
  } catch (error) {
    console.error('❌ Failed to fetch current matches:', error.message);
    return [];
  }
}

// Fetch upcoming fixtures
async function fetchUpcomingFixtures() {
  const url = 'https://webws.365scores.com/web/games/fixtures/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1';
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.games || [];
  } catch (error) {
    console.error('❌ Failed to fetch fixtures:', error.message);
    return [];
  }
}

// Fetch recent results
async function fetchRecentResults() {
  const url = 'https://webws.365scores.com/web/games/results/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1';
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.games || [];
  } catch (error) {
    console.error('❌ Failed to fetch results:', error.message);
    return [];
  }
}

// Fetch detailed match data
async function fetchMatchDetails(matchId) {
  const url = `https://webws.365scores.com/web/game/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&gameId=${matchId}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.game || null;
  } catch (error) {
    console.error(`❌ Failed to fetch match ${matchId} details:`, error.message);
    return null;
  }
}

// Fetch match statistics
async function fetchMatchStats(matchId) {
  const url = `https://webws.365scores.com/web/game/stats/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&games=${matchId}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.stats || null;
  } catch (error) {
    console.error(`❌ Failed to fetch match ${matchId} stats:`, error.message);
    return null;
  }
}

// Fetch standings for a competition
async function fetchStandings(competitionId, seasonNum = null, stageNum = null) {
  let url = `https://webws.365scores.com/web/standings/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitions=${competitionId}&live=false&isPreview=true`;
  
  if (stageNum) url += `&stageNum=${stageNum}`;
  if (seasonNum) url += `&seasonNum=${seasonNum}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.standings || [];
  } catch (error) {
    console.error(`❌ Failed to fetch standings for competition ${competitionId}:`, error.message);
    return [];
  }
}

// Process and upsert match data
async function upsertMatchData(supabase, matches) {
  if (!matches || matches.length === 0) return { success: true, count: 0 };

  try {
    const matchesToUpsert = matches.map(match => ({
      api_match_id: match.id,
      home_team_id: match.homeCompetitor?.id || null,
      away_team_id: match.awayCompetitor?.id || null,
      competition_id: match.competitionId,
      sport_id: match.sportId,
      season_num: safeParseInt(match.seasonNum),
      stage_num: safeParseInt(match.stageNum),
      round_num: safeParseInt(match.roundNum),
      round_name: match.roundName || null,
      competition_display_name: match.competitionDisplayName || null,
      start_time: parseDateTime(match.startTime),
      match_date: parseDate(match.startTime),
      match_time: parseTime(match.startTime),
      status_group: safeParseInt(match.statusGroup),
      status_text: match.statusText || null,
      short_status_text: match.shortStatusText || null,
      game_time_display: match.gameTimeDisplay || null,
      just_ended: match.justEnded || false,
      game_time: safeParseFloat(match.gameTime),
      show_countdown: match.showCountdown || false,
      home_score: match.homeCompetitor?.score >= 0 ? safeParseInt(match.homeCompetitor.score) : null,
      away_score: match.awayCompetitor?.score >= 0 ? safeParseInt(match.awayCompetitor.score) : null,
      minute: null, // Will be updated from detailed match data
      has_lineups: match.hasLineups || false,
      has_missing_players: match.hasMissingPlayers || false,
      has_field_positions: match.hasFieldPositions || false,
      has_tv_networks: match.hasTVNetworks || false,
      has_live_streaming: match.hasLiveStreaming || false,
      has_stats: match.hasStats || false,
      has_standings: match.hasStandings || false,
      has_brackets: match.hasBrackets || false,
      has_previous_meetings: match.hasPreviousMeetings || false,
      has_recent_matches: match.hasRecentMatches || false,
      has_bets: match.hasBets || false,
      has_player_bets: match.hasPlayerBets || false,
      has_news: match.hasNews || false,
      has_video: match.hasVideo || false,
      venue_id: match.venue?.id || null,
      is_home_away_inverted: match.isHomeAwayInverted || false,
      winner: safeParseInt(match.winner) || 0,
      home_away_team_order: safeParseInt(match.homeAwayTeamOrder) || 0
    }));

    const { error, count } = await supabase
      .from('matches')
      .upsert(matchesToUpsert, { onConflict: 'api_match_id' });

    if (error) {
      console.error('❌ Database error upserting matches:', error.message);
      return { success: false, error: error.message, count: 0 };
    }

    console.log(`✅ Upserted ${matchesToUpsert.length} matches`);
    return { success: true, count: matchesToUpsert.length };

  } catch (error) {
    console.error('❌ Failed to upsert match data:', error.message);
    return { success: false, error: error.message, count: 0 };
  }
}

// Process and upsert standings data
async function upsertStandingsData(supabase, standingsData) {
  if (!standingsData || standingsData.length === 0) return { success: true, count: 0 };

  try {
    let totalUpserted = 0;

    for (const standing of standingsData) {
      if (!standing.rows || standing.rows.length === 0) continue;

      const standingsToUpsert = standing.rows.map(row => ({
        competition_id: standing.competitionId,
        season_num: standing.seasonNum,
        stage_num: standing.stageNum,
        team_id: row.competitor?.id || null,
        position: safeParseInt(row.position),
        games_played: safeParseInt(row.gamePlayed) || 0,
        games_won: safeParseInt(row.gamesWon) || 0,
        games_lost: safeParseInt(row.gamesLost) || 0,
        games_even: safeParseInt(row.gamesEven) || 0,
        goals_for: safeParseInt(row.for) || 0,
        goals_against: safeParseInt(row.against) || 0,
        goal_ratio: safeParseFloat(row.ratio) || 0.0,
        points: safeParseFloat(row.points) || 0.0,
        strike: safeParseInt(row.strike) || 0,
        games_ot: safeParseInt(row.gamesOT) || 0,
        games_won_on_ot: safeParseInt(row.gamesWonOnOT) || 0,
        games_won_on_pen: safeParseInt(row.gamesWonOnPen) || 0,
        games_loss_on_ot: safeParseInt(row.gamesLossOnOT) || 0,
        games_loss_on_pen: safeParseInt(row.gamesLossOnPen) || 0,
        pct: row.pct || null,
        trend: safeParseInt(row.trend) || 0,
        next_match_id: row.nextMatch?.id || null,
        destination_num: safeParseInt(row.destinationNum),
        has_points_deduction: row.hasPointsDeduction || false,
        ppg: safeParseFloat(row.ppg) || 0.0,
        oppg: safeParseFloat(row.oppg) || 0.0,
        updated_at: new Date().toISOString()
      }));

      // Delete existing standings for this competition/season/stage
      await supabase
        .from('league_standings')
        .delete()
        .eq('competition_id', standing.competitionId)
        .eq('season_num', standing.seasonNum)
        .eq('stage_num', standing.stageNum);

      // Insert new standings
      const { error } = await supabase
        .from('league_standings')
        .insert(standingsToUpsert);

      if (error) {
        console.error(`❌ Database error upserting standings for competition ${standing.competitionId}:`, error.message);
        continue;
      }

      totalUpserted += standingsToUpsert.length;
      console.log(`✅ Upserted ${standingsToUpsert.length} standings for competition ${standing.competitionId}`);
    }

    return { success: true, count: totalUpserted };

  } catch (error) {
    console.error('❌ Failed to upsert standings data:', error.message);
    return { success: false, error: error.message, count: 0 };
  }
}

// Update match top performers
async function upsertTopPerformers(supabase, matchId, topPerformers) {
  if (!topPerformers || !topPerformers.categories) return { success: true, count: 0 };

  try {
    // Delete existing performers for this match
    await supabase
      .from('match_top_performers')
      .delete()
      .eq('match_id', matchId);

    const performersToInsert = [];

    topPerformers.categories.forEach(category => {
      if (category.homePlayer) {
        performersToInsert.push({
          match_id: matchId,
          player_id: category.homePlayer.id,
          team_side: 'home',
          category: category.name,
          player_name: category.homePlayer.name,
          position_name: category.homePlayer.positionName
        });
      }
      if (category.awayPlayer) {
        performersToInsert.push({
          match_id: matchId,
          player_id: category.awayPlayer.id,
          team_side: 'away',
          category: category.name,
          player_name: category.awayPlayer.name,
          position_name: category.awayPlayer.positionName
        });
      }
    });

    if (performersToInsert.length > 0) {
      const { error } = await supabase
        .from('match_top_performers')
        .insert(performersToInsert);

      if (error) {
        console.error(`❌ Database error upserting top performers for match ${matchId}:`, error.message);
        return { success: false, error: error.message, count: 0 };
      }
    }

    return { success: true, count: performersToInsert.length };

  } catch (error) {
    console.error(`❌ Failed to upsert top performers for match ${matchId}:`, error.message);
    return { success: false, error: error.message, count: 0 };
  }
}

// Main handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();
  const results = {
    matches: { current: 0, fixtures: 0, results: 0 },
    standings: 0,
    topPerformers: 0,
    errors: []
  };

  try {
    console.log('🚀 Starting real-time matches data update...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '', 
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get query parameters
    const url = new URL(req.url);
    const updateStandings = url.searchParams.get('standings') !== 'false'; // Default: true
    const updateTopPerformers = url.searchParams.get('topPerformers') !== 'false'; // Default: true
    const maxMatches = parseInt(url.searchParams.get('maxMatches') || '100'); // Limit for testing

    // Step 1: Fetch all match data in parallel
    console.log('📊 Fetching match data from multiple endpoints...');
    const [currentMatches, fixtures, recentResults] = await Promise.all([
      fetchCurrentMatches(),
      fetchUpcomingFixtures(),
      fetchRecentResults()
    ]);

    // Step 2: Combine and deduplicate matches
    const allMatches = [...currentMatches, ...fixtures, ...recentResults];
    const uniqueMatches = allMatches.filter((match, index, self) => 
      index === self.findIndex(m => m.id === match.id)
    ).slice(0, maxMatches);

    console.log(`📈 Found ${uniqueMatches.length} unique matches to update`);

    // Step 3: Upsert match data
    const matchResult = await upsertMatchData(supabase, uniqueMatches);
    results.matches.current = currentMatches.length;
    results.matches.fixtures = fixtures.length;
    results.matches.results = recentResults.length;

    if (!matchResult.success) {
      results.errors.push(`Matches: ${matchResult.error}`);
    }

    // Step 4: Update standings for active competitions
    if (updateStandings) {
      console.log('📊 Updating standings for active competitions...');
      
      const activeCompetitions = [...new Set(uniqueMatches.map(m => m.competitionId))].slice(0, 20);
      
      for (const competitionId of activeCompetitions) {
        try {
          const standingsData = await fetchStandings(competitionId);
          const standingsResult = await upsertStandingsData(supabase, standingsData);
          
          if (standingsResult.success) {
            results.standings += standingsResult.count;
          } else {
            results.errors.push(`Standings ${competitionId}: ${standingsResult.error}`);
          }
          
          // Small delay between standings requests
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          results.errors.push(`Standings ${competitionId}: ${error.message}`);
        }
      }
    }

    // Step 5: Update top performers for recent matches
    if (updateTopPerformers) {
      console.log('🌟 Updating top performers for recent matches...');
      
      const recentMatchIds = uniqueMatches
        .filter(m => m.statusGroup >= 2) // Scheduled or ongoing matches
        .map(m => m.id)
        .slice(0, 10); // Limit to 10 matches for performance

      for (const matchId of recentMatchIds) {
        try {
          const matchDetails = await fetchMatchDetails(matchId);
          if (matchDetails && matchDetails.topPerformers) {
            const performersResult = await upsertTopPerformers(supabase, matchId, matchDetails.topPerformers);
            
            if (performersResult.success) {
              results.topPerformers += performersResult.count;
            } else {
              results.errors.push(`Top performers ${matchId}: ${performersResult.error}`);
            }
          }
          
          // Small delay between requests
          await new Promise(resolve => setTimeout(resolve, 150));
        } catch (error) {
          results.errors.push(`Top performers ${matchId}: ${error.message}`);
        }
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`🎉 Real-time update completed in ${duration}s!`);
    console.log(`📊 Updated: ${matchResult.count} matches, ${results.standings} standings, ${results.topPerformers} top performers`);

    return new Response(JSON.stringify({
      success: true,
      message: 'Real-time matches data update completed',
      duration: `${duration}s`,
      results,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.error(`❌ Real-time update failed after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      partialResults: results,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
