// Import necessary libraries
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Helper function to add a delay between API calls
const delay = (ms) => new Promise((res) => setTimeout(res, ms));

// Helper function to safely parse integers
function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

// Helper function to safely convert date strings
function parseDate(dateString) {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0]; // Return date only
  } catch {
    return null;
  }
}
async function upsertPlayers(supabase, players, apiTeamId) {
  if (!players || players.length === 0) {
    console.log(`No players to update for team ${apiTeamId}.`);
    return { success: true, count: 0 };
  }

  try {
    // Filter out coaches and other staff from the athletes list
    const actualPlayers = players.filter((p) => {
      // More comprehensive filtering
      const isPlayer = p.formationPosition &&
                      p.formationPosition.name !== 'Coach' &&
                      p.formationPosition.name !== 'Assistant Coach' &&
                      p.position &&
                      p.position.name !== 'Management' &&
                      !p.position.isStaff;
      return isPlayer;
    });

    if (actualPlayers.length === 0) {
      console.log(`No actual players found for team ${apiTeamId} after filtering.`);
      return { success: true, count: 0 };
    }

    // Get the internal team_id from api_team_id for proper foreign key relationship
    const { data: teamData, error: teamError } = await supabase
      .from('teams')
      .select('team_id, api_team_id')
      .eq('api_team_id', apiTeamId)
      .single();

    if (teamError || !teamData) {
      console.warn(`⚠️ Team with api_team_id ${apiTeamId} not found in database. Skipping players.`);
      return { success: false, count: 0, error: 'Team not found' };
    }

    const internalTeamId = teamData.team_id;

    const playersToUpsert = actualPlayers.map((player) => ({
      api_player_id: player.id,
      player_name: player.name,
      team_id: internalTeamId, // Use internal team_id, not api_team_id
      position: player.position?.name || null,
      number: safeParseInt(player.jerseyNum), // Map to 'number' field
      nationality: null, // Not provided directly in API
      image_url: null, // Not provided in this API response
      cover_image_url: null, // Not provided in this API response
      bio: null, // Not provided in this API response
      age: safeParseInt(player.age),
      jersey_num: safeParseInt(player.jerseyNum),
      birthdate: parseDate(player.birthdate),
      gender: safeParseInt(player.gender),
      height: safeParseInt(player.height),
      short_name: player.shortName || null,
      name_for_url: player.nameForURL || null,
      sport_id: safeParseInt(player.sportId),
      club_id: safeParseInt(player.clubId), // Keep original club_id from API
      nationality_id: safeParseInt(player.nationalityId),
      national_team_id: safeParseInt(player.nationalTeamId),
      image_version: safeParseInt(player.imageVersion),
      formation_position_id: safeParseInt(player.formationPosition?.id),
      status: null // Not provided in this API response
    }));

    const { error, count } = await supabase
      .from('players')
      .upsert(playersToUpsert, { onConflict: 'api_player_id' });

    if (error) {
      console.error(`❌ Database error for team ${apiTeamId}:`, error.message);
      throw new Error(`Error upserting players for team ${apiTeamId}: ${error.message}`);
    }

    console.log(`✅ Upserted ${playersToUpsert.length} players for team ${apiTeamId}.`);
    return { success: true, count: playersToUpsert.length };
  } catch (error) {
    console.error(`❌ Failed to upsert players for team ${apiTeamId}:`, error.message);
    return { success: false, count: 0, error: error.message };
  }
}
// --- Enhanced Main Edge Function Handler ---
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();
  let totalPlayersUpserted = 0;
  let teamsProcessed = 0;
  let teamsFailed = 0;
  let teamsSkipped = 0;
  const failedTeams = [];
  const processedTeams = [];

  try {
    console.log('🚀 Starting enhanced players data fetch and insertion process...');

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Fetch all team IDs from database
    console.log('📊 Fetching all team IDs from the database...');
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('api_team_id, team_name')
      .not('api_team_id', 'is', null);

    if (teamsError) {
      throw new Error(`Failed to fetch teams: ${teamsError.message}`);
    }

    if (!teams || teams.length === 0) {
      const message = 'No teams found in the database to process. Please run the main data fetch function first.';
      console.log(`⚠️ ${message}`);
      return new Response(JSON.stringify({
        success: true,
        message: message,
        results: {
          teamsProcessed: 0,
          teamsFailed: 0,
          totalPlayersUpserted: 0
        }
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    console.log(`✅ Found ${teams.length} teams to process.`);

    // Process each team's squad
    for (let i = 0; i < teams.length; i++) {
      const team = teams[i];
      const teamId = team.api_team_id;
      const teamName = team.team_name;

      try {
        console.log(`\n🏟️ [${i + 1}/${teams.length}] Processing team: ${teamName} (ID: ${teamId})`);

        const SQUAD_API_URL = `https://webws.365scores.com/web/squads/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitors=${teamId}`;

        const response = await fetch(SQUAD_API_URL);
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.squads && data.squads.length > 0 && data.squads[0].athletes) {
          const result = await upsertPlayers(supabase, data.squads[0].athletes, teamId);

          if (result.success) {
            totalPlayersUpserted += result.count;
            processedTeams.push({
              teamId,
              teamName,
              playersCount: result.count
            });
            console.log(`   ✅ Successfully processed ${result.count} players`);
          } else {
            teamsSkipped++;
            console.log(`   ⚠️ Skipped team: ${result.error || 'Unknown error'}`);
          }
        } else {
          console.log(`   ℹ️ No squad data found for team ${teamName}`);
          teamsSkipped++;
        }

        teamsProcessed++;
      } catch (innerError) {
        console.error(`   ❌ Failed to process team ${teamName}:`, innerError.message);
        failedTeams.push({
          teamId,
          teamName,
          error: innerError.message
        });
        teamsFailed++;
      } finally {
        // Be respectful to the API - add delay between requests
        if (i < teams.length - 1) { // Don't delay after the last request
          await delay(300); // Increased delay to be more respectful
        }
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    const summaryMessage = `🎉 Players processing completed in ${duration}s!
📈 Results Summary:
  • Teams processed: ${teamsProcessed}/${teams.length}
  • Teams failed: ${teamsFailed}
  • Teams skipped: ${teamsSkipped}
  • Total players upserted: ${totalPlayersUpserted}`;

    console.log(summaryMessage);

    // Log failed teams for debugging
    if (failedTeams.length > 0) {
      console.log('\n❌ Failed teams:', failedTeams);
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Players processing completed successfully!',
      duration: `${duration}s`,
      results: {
        teamsTotal: teams.length,
        teamsProcessed,
        teamsFailed,
        teamsSkipped,
        totalPlayersUpserted,
        failedTeams: failedTeams.length > 0 ? failedTeams : undefined,
        processedTeams: processedTeams.slice(0, 10) // Show first 10 for brevity
      },
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.error(`❌ Critical error occurred after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      partialResults: {
        teamsProcessed,
        teamsFailed,
        teamsSkipped,
        totalPlayersUpserted,
        failedTeams
      },
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
