// Import necessary libraries
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};
// Helper function to add a delay between API calls
const delay = (ms)=>new Promise((res)=>setTimeout(res, ms));
async function upsertPlayers(supabase, players, teamId) {
  if (!players || players.length === 0) {
    console.log(`No players to update for team ${teamId}.`);
    return 0;
  }
  // Filter out coaches and other staff from the athletes list
  const actualPlayers = players.filter((p)=>p.formationPosition && p.formationPosition.name !== 'Coach' && p.position.name !== 'Management');
  if (actualPlayers.length === 0) {
    console.log(`No actual players found for team ${teamId} after filtering.`);
    return 0;
  }
  const playersToUpsert = actualPlayers.map((player)=>({
      api_player_id: player.id,
      player_name: player.name,
      team_id: player.clubId,
      position: player.position.name,
      jersey_num: player.jerseyNum,
      nationality_id: player.nationalityId,
      image_version: player.imageVersion,
      short_name: player.shortName,
      name_for_url: player.nameForURL,
      age: player.age,
      birthdate: player.birthdate,
      gender: player.gender,
      height: player.height,
      sport_id: player.sportId,
      national_team_id: player.nationalTeamId,
      formation_position_id: player.formationPosition.id
    }));
  const { error, count } = await supabase.from('players').upsert(playersToUpsert, {
    onConflict: 'api_player_id'
  });
  if (error) {
    // Log the detailed error but throw a more general message to be caught by the handler
    console.error(`Database error for team ${teamId}:`, error.message);
    throw new Error(`Error upserting players for team ${teamId}.`);
  }
  console.log(`Upserted ${count ?? 0} players for team ${teamId}.`);
  return count ?? 0;
}
// --- Main Edge Function Handler ---
serve(async (req)=>{
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  let totalPlayersUpserted = 0;
  let teamsProcessed = 0;
  let teamsFailed = 0;
  try {
    // 1. Initialize Supabase client securely
    const supabase = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '');
    // 2. Fetch all team IDs from your database
    console.log("Fetching all team IDs from the database...");
    const { data: teams, error: teamsError } = await supabase.from('teams').select('api_team_id').not('api_team_id', 'is', null);
    if (teamsError) {
      throw new Error(`Failed to fetch teams: ${teamsError.message}`);
    }
    if (!teams || teams.length === 0) {
      return new Response(JSON.stringify({
        message: "No teams found in the database to process."
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 200
      });
    }
    console.log(`Found ${teams.length} teams to process.`);
    // 3. Loop through each team and fetch its squad
    for (const team of teams){
      try {
        const teamId = team.api_team_id;
        const SQUAD_API_URL = `https://webws.365scores.com/web/squads/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitors=${teamId}`;
        console.log(`Fetching squad for team ID: ${teamId}...`);
        const response = await fetch(SQUAD_API_URL);
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }
        const data = await response.json();
        if (data.squads && data.squads.length > 0 && data.squads[0].athletes) {
          const upsertedCount = await upsertPlayers(supabase, data.squads[0].athletes, teamId);
          totalPlayersUpserted += upsertedCount;
        } else {
          console.log(`No squad data found for team ${teamId}.`);
        }
        teamsProcessed++;
      } catch (innerError) {
        // Log the error for the specific team and continue
        console.error(`Failed to process team ${team.api_team_id}:`, innerError.message);
        teamsFailed++;
      } finally{
        // Be a good API citizen: wait a moment before the next request
        await delay(200);
      }
    }
    const summaryMessage = `Processing complete. Processed: ${teamsProcessed} teams. Failed: ${teamsFailed} teams. Total players upserted: ${totalPlayersUpserted}.`;
    console.log(summaryMessage);
    return new Response(JSON.stringify({
      message: summaryMessage
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });
  } catch (error) {
    console.error('A critical error occurred in the handler:', error.message);
    return new Response(JSON.stringify({
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
