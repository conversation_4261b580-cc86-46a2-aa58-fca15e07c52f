// Comprehensive Standings Updater Function
// Updates league standings for all active competitions

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Helper functions
function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

function safeParseFloat(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? null : parsed;
}

// Fetch standings for a specific competition
async function fetchStandings(competitionId, seasonNum = null, stageNum = null) {
  let url = `https://webws.365scores.com/web/standings/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitions=${competitionId}&live=false&isPreview=true`;
  
  if (stageNum) url += `&stageNum=${stageNum}`;
  if (seasonNum) url += `&seasonNum=${seasonNum}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.standings || [];
  } catch (error) {
    console.error(`❌ Failed to fetch standings for competition ${competitionId}:`, error.message);
    return [];
  }
}

// Get active competitions that have standings
async function getActiveCompetitions(supabase, limit = 100) {
  try {
    // Get competitions that have recent matches and standings enabled
    const { data: competitions, error } = await supabase
      .from('leagues')
      .select('api_league_id, league_name, has_standings, current_season_num, current_stage_num')
      .eq('has_standings', true)
      .eq('is_active', true)
      .not('api_league_id', 'is', null)
      .order('popularity_rank', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('❌ Error fetching active competitions:', error.message);
      return [];
    }

    return competitions || [];
  } catch (error) {
    console.error('❌ Failed to get active competitions:', error.message);
    return [];
  }
}

// Process and upsert standings data for a competition
async function upsertStandingsForCompetition(supabase, competitionId, competitionName) {
  try {
    console.log(`📊 Updating standings for ${competitionName} (${competitionId})...`);
    
    const standingsData = await fetchStandings(competitionId);
    
    if (!standingsData || standingsData.length === 0) {
      console.log(`   ℹ️ No standings data found for ${competitionName}`);
      return { success: true, count: 0, note: 'No standings data' };
    }

    let totalUpserted = 0;
    const errors = [];

    for (const standing of standingsData) {
      if (!standing.rows || standing.rows.length === 0) continue;

      try {
        // First, get team mappings to convert API team IDs to internal team IDs
        const apiTeamIds = standing.rows.map(row => row.competitor?.id).filter(id => id);
        
        const { data: teamMappings, error: teamError } = await supabase
          .from('teams')
          .select('team_id, api_team_id')
          .in('api_team_id', apiTeamIds);

        if (teamError) {
          console.error(`   ❌ Error fetching team mappings:`, teamError.message);
          continue;
        }

        const teamIdMap = {};
        teamMappings?.forEach(team => {
          teamIdMap[team.api_team_id] = team.team_id;
        });

        const standingsToUpsert = standing.rows
          .filter(row => row.competitor?.id && teamIdMap[row.competitor.id])
          .map(row => ({
            competition_id: standing.competitionId,
            season_num: standing.seasonNum,
            stage_num: standing.stageNum,
            team_id: teamIdMap[row.competitor.id], // Use internal team_id
            position: safeParseInt(row.position),
            games_played: safeParseInt(row.gamePlayed) || 0,
            games_won: safeParseInt(row.gamesWon) || 0,
            games_lost: safeParseInt(row.gamesLost) || 0,
            games_even: safeParseInt(row.gamesEven) || 0,
            goals_for: safeParseInt(row.for) || 0,
            goals_against: safeParseInt(row.against) || 0,
            goal_ratio: safeParseFloat(row.ratio) || 0.0,
            points: safeParseFloat(row.points) || 0.0,
            strike: safeParseInt(row.strike) || 0,
            games_ot: safeParseInt(row.gamesOT) || 0,
            games_won_on_ot: safeParseInt(row.gamesWonOnOT) || 0,
            games_won_on_pen: safeParseInt(row.gamesWonOnPen) || 0,
            games_loss_on_ot: safeParseInt(row.gamesLossOnOT) || 0,
            games_loss_on_pen: safeParseInt(row.gamesLossOnPen) || 0,
            pct: row.pct || null,
            trend: safeParseInt(row.trend) || 0,
            next_match_id: null, // Will be populated separately if needed
            destination_num: safeParseInt(row.destinationNum),
            has_points_deduction: row.hasPointsDeduction || false,
            ppg: safeParseFloat(row.ppg) || 0.0,
            oppg: safeParseFloat(row.oppg) || 0.0,
            updated_at: new Date().toISOString()
          }));

        if (standingsToUpsert.length === 0) {
          console.log(`   ⚠️ No valid team mappings found for ${competitionName} standings`);
          continue;
        }

        // Delete existing standings for this competition/season/stage
        const { error: deleteError } = await supabase
          .from('league_standings')
          .delete()
          .eq('competition_id', standing.competitionId)
          .eq('season_num', standing.seasonNum)
          .eq('stage_num', standing.stageNum);

        if (deleteError) {
          console.error(`   ❌ Error deleting existing standings:`, deleteError.message);
        }

        // Insert new standings
        const { error: insertError } = await supabase
          .from('league_standings')
          .insert(standingsToUpsert);

        if (insertError) {
          console.error(`   ❌ Database error inserting standings:`, insertError.message);
          errors.push(`Season ${standing.seasonNum}, Stage ${standing.stageNum}: ${insertError.message}`);
          continue;
        }

        totalUpserted += standingsToUpsert.length;
        console.log(`   ✅ Inserted ${standingsToUpsert.length} standings for season ${standing.seasonNum}, stage ${standing.stageNum}`);

      } catch (error) {
        console.error(`   ❌ Error processing standings group:`, error.message);
        errors.push(`Processing error: ${error.message}`);
      }
    }

    return { 
      success: true, 
      count: totalUpserted, 
      errors: errors.length > 0 ? errors : null,
      standingsGroups: standingsData.length
    };

  } catch (error) {
    console.error(`❌ Failed to upsert standings for competition ${competitionId}:`, error.message);
    return { success: false, error: error.message, count: 0 };
  }
}

// Update standings for competitions with recent matches
async function updateStandingsForActiveMatches(supabase, limit = 20) {
  try {
    // Get competitions that have had matches in the last 7 days
    const { data: recentCompetitions, error } = await supabase
      .from('matches')
      .select('competition_id')
      .gte('start_time', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .not('competition_id', 'is', null);

    if (error) {
      console.error('❌ Error fetching recent competitions:', error.message);
      return [];
    }

    // Get unique competition IDs
    const uniqueCompetitionIds = [...new Set(recentCompetitions?.map(m => m.competition_id) || [])];
    
    // Get competition details
    const { data: competitions, error: compError } = await supabase
      .from('leagues')
      .select('api_league_id, league_name, has_standings')
      .in('api_league_id', uniqueCompetitionIds)
      .eq('has_standings', true)
      .limit(limit);

    if (compError) {
      console.error('❌ Error fetching competition details:', compError.message);
      return [];
    }

    return competitions || [];

  } catch (error) {
    console.error('❌ Failed to get competitions with recent matches:', error.message);
    return [];
  }
}

// Main handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();
  const results = {
    competitionsProcessed: 0,
    totalStandingsUpdated: 0,
    errors: [],
    competitionResults: []
  };

  try {
    console.log('📊 Starting comprehensive standings update...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '', 
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get query parameters
    const url = new URL(req.url);
    const specificCompetitionId = url.searchParams.get('competitionId');
    const maxCompetitions = parseInt(url.searchParams.get('maxCompetitions') || '30');
    const recentOnly = url.searchParams.get('recentOnly') === 'true'; // Only competitions with recent matches

    let competitionsToUpdate = [];

    if (specificCompetitionId) {
      // Update specific competition
      const { data: competition, error } = await supabase
        .from('leagues')
        .select('api_league_id, league_name, has_standings')
        .eq('api_league_id', parseInt(specificCompetitionId))
        .single();

      if (competition && competition.has_standings) {
        competitionsToUpdate = [competition];
      }
      console.log(`🎯 Updating specific competition: ${specificCompetitionId}`);
    } else if (recentOnly) {
      // Get competitions with recent matches
      competitionsToUpdate = await updateStandingsForActiveMatches(supabase, maxCompetitions);
      console.log(`📈 Found ${competitionsToUpdate.length} competitions with recent matches`);
    } else {
      // Get all active competitions with standings
      competitionsToUpdate = await getActiveCompetitions(supabase, maxCompetitions);
      console.log(`📊 Found ${competitionsToUpdate.length} active competitions with standings`);
    }

    if (competitionsToUpdate.length === 0) {
      return new Response(JSON.stringify({
        success: true,
        message: 'No competitions found to update',
        results,
        timestamp: new Date().toISOString()
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    // Process each competition
    for (const competition of competitionsToUpdate) {
      const competitionId = competition.api_league_id;
      const competitionName = competition.league_name;
      
      try {
        const result = await upsertStandingsForCompetition(supabase, competitionId, competitionName);
        
        results.competitionResults.push({
          competitionId,
          competitionName,
          success: result.success,
          standingsUpdated: result.count,
          standingsGroups: result.standingsGroups,
          errors: result.errors,
          note: result.note
        });

        if (result.success) {
          results.competitionsProcessed++;
          results.totalStandingsUpdated += result.count;
        } else {
          results.errors.push(`${competitionName}: ${result.error}`);
        }

        // Respectful delay between competitions
        await new Promise(resolve => setTimeout(resolve, 300));

      } catch (error) {
        console.error(`❌ Error processing competition ${competitionName}:`, error.message);
        results.errors.push(`${competitionName}: ${error.message}`);
        
        results.competitionResults.push({
          competitionId,
          competitionName,
          success: false,
          standingsUpdated: 0,
          error: error.message
        });
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`🎉 Standings update completed in ${duration}s!`);
    console.log(`📊 Processed: ${results.competitionsProcessed}/${competitionsToUpdate.length} competitions`);
    console.log(`📈 Updated: ${results.totalStandingsUpdated} total standings`);

    return new Response(JSON.stringify({
      success: true,
      message: 'Comprehensive standings update completed',
      duration: `${duration}s`,
      results,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.error(`❌ Standings update failed after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      partialResults: results,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
