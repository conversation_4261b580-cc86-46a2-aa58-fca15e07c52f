# ⚡ Real-Time Football Data Functions Guide

## 🎯 **Overview**

These functions provide near real-time updates for your football app, fetching live matches, standings, lineups, and statistics every 1-5 minutes.

---

## 🚀 **Available Functions**

### **1. Master Real-Time Updater** (`master_realtime_updater_function.txt`)
**⚡ Primary Function - Run Every 1-5 Minutes**
- **Purpose**: Complete real-time data pipeline
- **Updates**: Matches, standings, top performers
- **Optimized**: Fast execution (30-60 seconds)
- **Best For**: Main real-time updates

**Usage:**
```bash
# Full real-time update (recommended)
curl -X POST https://your-project.supabase.co/functions/v1/master_realtime_updater_function

# Customize what to update
curl -X POST "https://your-project.supabase.co/functions/v1/master_realtime_updater_function?standings=true&topPerformers=true&maxStandingsCompetitions=10"

# Quick matches-only update
curl -X POST "https://your-project.supabase.co/functions/v1/master_realtime_updater_function?standings=false&topPerformers=false"
```

### **2. Real-Time Matches Updater** (`realtime_matches_updater_function.txt`)
**⚡ Matches Focus - High Frequency Updates**
- **Purpose**: Comprehensive match data updates
- **Updates**: Current matches, fixtures, results, standings
- **Features**: Top performers, match details
- **Best For**: Match-focused updates

**Usage:**
```bash
# Full matches update
curl -X POST https://your-project.supabase.co/functions/v1/realtime_matches_updater_function

# Limit for faster execution
curl -X POST "https://your-project.supabase.co/functions/v1/realtime_matches_updater_function?maxMatches=50&topPerformers=false"
```

### **3. Detailed Match Data Function** (`detailed_match_data_function.txt`)
**⚡ Deep Match Analysis - On-Demand**
- **Purpose**: Detailed match information and statistics
- **Updates**: Live stats, lineups, top performers with stats
- **Features**: Match-specific deep dive
- **Best For**: Specific match analysis

**Usage:**
```bash
# Update specific match
curl -X POST "https://your-project.supabase.co/functions/v1/detailed_match_data_function?matchId=4544000"

# Update recent active matches
curl -X POST "https://your-project.supabase.co/functions/v1/detailed_match_data_function?maxMatches=10"

# Skip detailed stats for faster execution
curl -X POST "https://your-project.supabase.co/functions/v1/detailed_match_data_function?stats=false"
```

### **4. Standings Updater Function** (`standings_updater_function.txt`)
**⚡ League Tables - Periodic Updates**
- **Purpose**: Comprehensive league standings updates
- **Updates**: All league tables and positions
- **Features**: Team mappings, detailed standings data
- **Best For**: League table updates

**Usage:**
```bash
# Update all active league standings
curl -X POST https://your-project.supabase.co/functions/v1/standings_updater_function

# Update specific competition
curl -X POST "https://your-project.supabase.co/functions/v1/standings_updater_function?competitionId=557"

# Only competitions with recent matches
curl -X POST "https://your-project.supabase.co/functions/v1/standings_updater_function?recentOnly=true&maxCompetitions=15"
```

---

## 📊 **Data Updated by Each Function**

### **Database Tables Populated:**

| Function | Matches | Standings | Top Performers | Performer Stats |
|----------|---------|-----------|----------------|-----------------|
| **Master Real-Time** | ✅ | ✅ | ✅ | ❌ |
| **Matches Updater** | ✅ | ✅ | ✅ | ❌ |
| **Detailed Match** | ✅ | ❌ | ✅ | ✅ |
| **Standings Updater** | ❌ | ✅ | ❌ | ❌ |

### **Match Data Fields Updated:**
- Live scores and status
- Match time and minute
- Team lineups availability
- Statistics availability
- TV networks and streaming
- Match events and top performers

### **Standings Data Fields Updated:**
- Team positions and points
- Games played, won, lost, drawn
- Goals for/against and goal difference
- Recent form and trends
- Next match information

---

## ⏰ **Recommended Update Schedule**

### **For Live Match Days:**
```bash
# Every 1 minute during live matches
*/1 * * * * curl -X POST https://your-project.supabase.co/functions/v1/master_realtime_updater_function

# Every 5 minutes for detailed match data
*/5 * * * * curl -X POST https://your-project.supabase.co/functions/v1/detailed_match_data_function
```

### **For Regular Days:**
```bash
# Every 5 minutes for general updates
*/5 * * * * curl -X POST https://your-project.supabase.co/functions/v1/master_realtime_updater_function

# Every 30 minutes for standings
*/30 * * * * curl -X POST https://your-project.supabase.co/functions/v1/standings_updater_function?recentOnly=true
```

### **For Off-Season/Low Activity:**
```bash
# Every 15 minutes
*/15 * * * * curl -X POST https://your-project.supabase.co/functions/v1/master_realtime_updater_function?maxStandingsCompetitions=5
```

---

## 🔧 **Configuration Options**

### **Master Real-Time Updater Parameters:**
- `standings`: Enable/disable standings updates (default: true)
- `topPerformers`: Enable/disable top performers (default: true)
- `maxStandingsCompetitions`: Limit competitions for standings (default: 8)
- `maxTopPerformersMatches`: Limit matches for top performers (default: 5)

### **Matches Updater Parameters:**
- `standings`: Include standings updates (default: true)
- `topPerformers`: Include top performers (default: true)
- `maxMatches`: Limit number of matches (default: 100)

### **Detailed Match Parameters:**
- `matchId`: Update specific match
- `maxMatches`: Limit matches to update (default: 20)
- `stats`: Include detailed statistics (default: true)

### **Standings Updater Parameters:**
- `competitionId`: Update specific competition
- `recentOnly`: Only competitions with recent matches (default: false)
- `maxCompetitions`: Limit competitions (default: 30)

---

## 📈 **Performance Optimization**

### **Fast Updates (< 30 seconds):**
```bash
# Minimal update for high frequency
curl -X POST "https://your-project.supabase.co/functions/v1/master_realtime_updater_function?maxStandingsCompetitions=3&maxTopPerformersMatches=3"
```

### **Comprehensive Updates (1-2 minutes):**
```bash
# Full update for periodic comprehensive refresh
curl -X POST "https://your-project.supabase.co/functions/v1/master_realtime_updater_function?maxStandingsCompetitions=15&maxTopPerformersMatches=10"
```

### **Match-Day Optimization:**
```bash
# Focus on live matches during match days
curl -X POST "https://your-project.supabase.co/functions/v1/realtime_matches_updater_function?maxMatches=30&standings=false"
```

---

## 🚨 **Error Handling & Monitoring**

### **Success Indicators:**
- ✅ `success: true` in response
- ✅ Reasonable execution time (< 60s for master function)
- ✅ Non-zero counts for updated data
- ✅ Minimal errors in results

### **Common Issues & Solutions:**

**Issue**: "Timeout after 60 seconds"
**Solution**: Reduce parameters (maxMatches, maxCompetitions)

**Issue**: "Foreign key constraint violations"
**Solution**: Run basic data functions first to ensure teams/leagues exist

**Issue**: "API rate limit exceeded"
**Solution**: Increase delays between requests or reduce frequency

**Issue**: "No data updated"
**Solution**: Check if competitions/matches exist in your database

---

## 🎯 **Integration Examples**

### **React App Integration:**
```javascript
// Update every minute during live matches
useEffect(() => {
  const interval = setInterval(async () => {
    try {
      const response = await fetch('/api/update-realtime');
      const data = await response.json();
      if (data.success) {
        // Refresh your app data
        refreshMatchData();
      }
    } catch (error) {
      console.error('Real-time update failed:', error);
    }
  }, 60000); // 1 minute

  return () => clearInterval(interval);
}, []);
```

### **Webhook Integration:**
```bash
# Set up webhook to call your app after updates
curl -X POST "https://your-project.supabase.co/functions/v1/master_realtime_updater_function" \
  -H "webhook-url: https://your-app.com/api/data-updated"
```

---

## 🎉 **Expected Results**

### **Data Freshness:**
- **Live Matches**: Updated every 1-5 minutes
- **Standings**: Updated every 5-30 minutes
- **Match Details**: Updated every 1-10 minutes
- **Top Performers**: Updated every 1-5 minutes

### **User Experience:**
- Near real-time score updates
- Live match status changes
- Updated league tables
- Fresh player statistics
- Current match lineups

Your users will experience a truly live football app with data that's always current! ⚽🚀
