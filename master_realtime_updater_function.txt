// Master Real-Time Updater Function
// Orchestrates all real-time data updates for matches, standings, and statistics
// Designed to run every 1-5 minutes for near real-time updates

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Helper functions
function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

function safeParseFloat(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? null : parsed;
}

function parseDateTime(dateString) {
  if (!dateString) return null;
  try {
    return new Date(dateString).toISOString();
  } catch {
    return null;
  }
}

// Fetch current/live matches
async function fetchCurrentMatches() {
  const url = 'https://webws.365scores.com/web/games/current/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1';
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.games || [];
  } catch (error) {
    console.error('❌ Failed to fetch current matches:', error.message);
    return [];
  }
}

// Fetch upcoming fixtures (next 24 hours)
async function fetchUpcomingFixtures() {
  const url = 'https://webws.365scores.com/web/games/fixtures/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1';
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.games || [];
  } catch (error) {
    console.error('❌ Failed to fetch fixtures:', error.message);
    return [];
  }
}

// Fetch recent results
async function fetchRecentResults() {
  const url = 'https://webws.365scores.com/web/games/results/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1';
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.games || [];
  } catch (error) {
    console.error('❌ Failed to fetch results:', error.message);
    return [];
  }
}

// Fetch detailed match data
async function fetchMatchDetails(matchId) {
  const url = `https://webws.365scores.com/web/game/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&gameId=${matchId}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.game || null;
  } catch (error) {
    console.error(`❌ Failed to fetch match ${matchId} details:`, error.message);
    return null;
  }
}

// Fetch standings for a competition
async function fetchStandings(competitionId) {
  const url = `https://webws.365scores.com/web/standings/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitions=${competitionId}&live=false&isPreview=true`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`API error: ${response.status}`);
    
    const data = await response.json();
    return data.standings || [];
  } catch (error) {
    console.error(`❌ Failed to fetch standings for competition ${competitionId}:`, error.message);
    return [];
  }
}

// Update matches data
async function updateMatches(supabase, matches) {
  if (!matches || matches.length === 0) return { success: true, count: 0 };

  try {
    const matchesToUpsert = matches.map(match => ({
      api_match_id: match.id,
      home_team_id: match.homeCompetitor?.id || null,
      away_team_id: match.awayCompetitor?.id || null,
      competition_id: match.competitionId,
      sport_id: match.sportId,
      season_num: safeParseInt(match.seasonNum),
      stage_num: safeParseInt(match.stageNum),
      round_num: safeParseInt(match.roundNum),
      round_name: match.roundName || null,
      competition_display_name: match.competitionDisplayName || null,
      start_time: parseDateTime(match.startTime),
      match_date: match.startTime ? new Date(match.startTime).toISOString().split('T')[0] : null,
      match_time: match.startTime ? new Date(match.startTime).toISOString().split('T')[1].split('.')[0] : null,
      status_group: safeParseInt(match.statusGroup),
      status_text: match.statusText || null,
      short_status_text: match.shortStatusText || null,
      game_time_display: match.gameTimeDisplay || null,
      just_ended: match.justEnded || false,
      game_time: safeParseFloat(match.gameTime),
      show_countdown: match.showCountdown || false,
      home_score: match.homeCompetitor?.score >= 0 ? safeParseInt(match.homeCompetitor.score) : null,
      away_score: match.awayCompetitor?.score >= 0 ? safeParseInt(match.awayCompetitor.score) : null,
      has_lineups: match.hasLineups || false,
      has_stats: match.hasStats || false,
      has_standings: match.hasStandings || false,
      has_tv_networks: match.hasTVNetworks || false,
      has_live_streaming: match.hasLiveStreaming || false,
      winner: safeParseInt(match.winner) || 0,
      is_home_away_inverted: match.isHomeAwayInverted || false
    }));

    const { error } = await supabase
      .from('matches')
      .upsert(matchesToUpsert, { onConflict: 'api_match_id' });

    if (error) {
      console.error('❌ Database error upserting matches:', error.message);
      return { success: false, error: error.message, count: 0 };
    }

    return { success: true, count: matchesToUpsert.length };

  } catch (error) {
    console.error('❌ Failed to update matches:', error.message);
    return { success: false, error: error.message, count: 0 };
  }
}

// Update standings for active competitions
async function updateStandingsData(supabase, competitionIds, maxCompetitions = 10) {
  if (!competitionIds || competitionIds.length === 0) return { success: true, count: 0 };

  try {
    let totalUpdated = 0;
    const errors = [];

    // Limit competitions to avoid timeout
    const limitedCompetitions = competitionIds.slice(0, maxCompetitions);

    for (const competitionId of limitedCompetitions) {
      try {
        const standingsData = await fetchStandings(competitionId);
        
        if (!standingsData || standingsData.length === 0) continue;

        for (const standing of standingsData) {
          if (!standing.rows || standing.rows.length === 0) continue;

          // Get team mappings
          const apiTeamIds = standing.rows.map(row => row.competitor?.id).filter(id => id);
          
          const { data: teamMappings } = await supabase
            .from('teams')
            .select('team_id, api_team_id')
            .in('api_team_id', apiTeamIds);

          const teamIdMap = {};
          teamMappings?.forEach(team => {
            teamIdMap[team.api_team_id] = team.team_id;
          });

          const standingsToUpsert = standing.rows
            .filter(row => row.competitor?.id && teamIdMap[row.competitor.id])
            .map(row => ({
              competition_id: standing.competitionId,
              season_num: standing.seasonNum,
              stage_num: standing.stageNum,
              team_id: teamIdMap[row.competitor.id],
              position: safeParseInt(row.position),
              games_played: safeParseInt(row.gamePlayed) || 0,
              games_won: safeParseInt(row.gamesWon) || 0,
              games_lost: safeParseInt(row.gamesLost) || 0,
              games_even: safeParseInt(row.gamesEven) || 0,
              goals_for: safeParseInt(row.for) || 0,
              goals_against: safeParseInt(row.against) || 0,
              goal_ratio: safeParseFloat(row.ratio) || 0.0,
              points: safeParseFloat(row.points) || 0.0,
              updated_at: new Date().toISOString()
            }));

          if (standingsToUpsert.length > 0) {
            // Delete existing standings
            await supabase
              .from('league_standings')
              .delete()
              .eq('competition_id', standing.competitionId)
              .eq('season_num', standing.seasonNum)
              .eq('stage_num', standing.stageNum);

            // Insert new standings
            const { error } = await supabase
              .from('league_standings')
              .insert(standingsToUpsert);

            if (error) {
              errors.push(`Competition ${competitionId}: ${error.message}`);
            } else {
              totalUpdated += standingsToUpsert.length;
            }
          }
        }

        // Small delay between competitions
        await new Promise(resolve => setTimeout(resolve, 200));

      } catch (error) {
        errors.push(`Competition ${competitionId}: ${error.message}`);
      }
    }

    return { 
      success: true, 
      count: totalUpdated, 
      errors: errors.length > 0 ? errors : null,
      competitionsProcessed: limitedCompetitions.length
    };

  } catch (error) {
    console.error('❌ Failed to update standings:', error.message);
    return { success: false, error: error.message, count: 0 };
  }
}

// Update top performers for live/recent matches
async function updateTopPerformers(supabase, liveMatches, maxMatches = 5) {
  if (!liveMatches || liveMatches.length === 0) return { success: true, count: 0 };

  try {
    let totalUpdated = 0;
    const errors = [];

    // Limit to most important matches
    const limitedMatches = liveMatches.slice(0, maxMatches);

    for (const match of limitedMatches) {
      try {
        const matchDetails = await fetchMatchDetails(match.id);
        
        if (!matchDetails || !matchDetails.topPerformers) continue;

        // Delete existing performers
        await supabase
          .from('match_top_performers')
          .delete()
          .eq('match_id', match.id);

        const performersToInsert = [];

        matchDetails.topPerformers.categories?.forEach(category => {
          if (category.homePlayer) {
            performersToInsert.push({
              match_id: match.id,
              player_id: category.homePlayer.id,
              team_side: 'home',
              category: category.name,
              player_name: category.homePlayer.name,
              position_name: category.homePlayer.positionName
            });
          }
          if (category.awayPlayer) {
            performersToInsert.push({
              match_id: match.id,
              player_id: category.awayPlayer.id,
              team_side: 'away',
              category: category.name,
              player_name: category.awayPlayer.name,
              position_name: category.awayPlayer.positionName
            });
          }
        });

        if (performersToInsert.length > 0) {
          const { error } = await supabase
            .from('match_top_performers')
            .insert(performersToInsert);

          if (error) {
            errors.push(`Match ${match.id}: ${error.message}`);
          } else {
            totalUpdated += performersToInsert.length;
          }
        }

        // Small delay between matches
        await new Promise(resolve => setTimeout(resolve, 150));

      } catch (error) {
        errors.push(`Match ${match.id}: ${error.message}`);
      }
    }

    return { 
      success: true, 
      count: totalUpdated, 
      errors: errors.length > 0 ? errors : null,
      matchesProcessed: limitedMatches.length
    };

  } catch (error) {
    console.error('❌ Failed to update top performers:', error.message);
    return { success: false, error: error.message, count: 0 };
  }
}

// Main handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();
  const results = {
    matches: { current: 0, fixtures: 0, results: 0, total: 0 },
    standings: { count: 0, competitions: 0 },
    topPerformers: { count: 0, matches: 0 },
    errors: []
  };

  try {
    console.log('🚀 Starting master real-time update...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '', 
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get query parameters for customization
    const url = new URL(req.url);
    const updateStandings = url.searchParams.get('standings') !== 'false'; // Default: true
    const updateTopPerformers = url.searchParams.get('topPerformers') !== 'false'; // Default: true
    const maxStandingsCompetitions = parseInt(url.searchParams.get('maxStandingsCompetitions') || '8');
    const maxTopPerformersMatches = parseInt(url.searchParams.get('maxTopPerformersMatches') || '5');

    // Step 1: Fetch all match data in parallel
    console.log('📊 Fetching match data from all endpoints...');
    const [currentMatches, fixtures, recentResults] = await Promise.all([
      fetchCurrentMatches(),
      fetchUpcomingFixtures(),
      fetchRecentResults()
    ]);

    // Step 2: Combine and deduplicate matches
    const allMatches = [...currentMatches, ...fixtures, ...recentResults];
    const uniqueMatches = allMatches.filter((match, index, self) => 
      index === self.findIndex(m => m.id === match.id)
    );

    console.log(`📈 Found ${uniqueMatches.length} unique matches (${currentMatches.length} live, ${fixtures.length} fixtures, ${recentResults.length} results)`);

    // Step 3: Update matches
    const matchResult = await updateMatches(supabase, uniqueMatches);
    results.matches.current = currentMatches.length;
    results.matches.fixtures = fixtures.length;
    results.matches.results = recentResults.length;
    results.matches.total = matchResult.count;

    if (!matchResult.success) {
      results.errors.push(`Matches: ${matchResult.error}`);
    }

    // Step 4: Update standings for active competitions
    if (updateStandings) {
      console.log('📊 Updating standings for active competitions...');
      
      const activeCompetitions = [...new Set(uniqueMatches.map(m => m.competitionId))];
      const standingsResult = await updateStandingsData(supabase, activeCompetitions, maxStandingsCompetitions);
      
      results.standings.count = standingsResult.count;
      results.standings.competitions = standingsResult.competitionsProcessed || 0;
      
      if (!standingsResult.success) {
        results.errors.push(`Standings: ${standingsResult.error}`);
      } else if (standingsResult.errors) {
        results.errors.push(...standingsResult.errors);
      }
    }

    // Step 5: Update top performers for live/recent matches
    if (updateTopPerformers) {
      console.log('🌟 Updating top performers for live matches...');
      
      const liveMatches = currentMatches.filter(m => m.statusGroup === 1); // Live matches
      const performersResult = await updateTopPerformers(supabase, liveMatches, maxTopPerformersMatches);
      
      results.topPerformers.count = performersResult.count;
      results.topPerformers.matches = performersResult.matchesProcessed || 0;
      
      if (!performersResult.success) {
        results.errors.push(`Top performers: ${performersResult.error}`);
      } else if (performersResult.errors) {
        results.errors.push(...performersResult.errors);
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`🎉 Master real-time update completed in ${duration}s!`);
    console.log(`📊 Updated: ${results.matches.total} matches, ${results.standings.count} standings, ${results.topPerformers.count} top performers`);

    return new Response(JSON.stringify({
      success: true,
      message: 'Master real-time update completed successfully',
      duration: `${duration}s`,
      results,
      timestamp: new Date().toISOString(),
      nextUpdateRecommended: new Date(Date.now() + 60000).toISOString() // Suggest next update in 1 minute
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.error(`❌ Master real-time update failed after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      partialResults: results,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
