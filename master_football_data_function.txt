// Master Football Data Fetching Function - Complete Data Pipeline
// This function orchestrates the complete data fetching process:
// 1. Fetches and inserts matches, teams, leagues, and countries
// 2. Then fetches and inserts all players for those teams
// 3. Provides comprehensive logging and error handling

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// API endpoints
const MAIN_API_URL = 'https://webws.365scores.com/web/games/allscores/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&sports=1&showOdds=true&withTop=true';

// Helper functions
const delay = (ms) => new Promise((res) => setTimeout(res, ms));

function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

function safeParseFloat(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? null : parsed;
}

function parseDate(dateString) {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date.toISOString();
  } catch {
    return null;
  }
}

function parseDateOnly(dateString) {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
  } catch {
    return null;
  }
}

// Step 1: Fetch and insert main data (countries, leagues, teams, matches)
async function fetchMainData(supabase) {
  console.log('🚀 Step 1: Fetching main football data...');
  
  try {
    const response = await fetch(MAIN_API_URL);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`📊 Fetched: ${data.countries?.length || 0} countries, ${data.competitions?.length || 0} leagues, ${data.games?.length || 0} games`);

    // Process data in correct order (respecting foreign key dependencies)
    const results = {
      countries: await upsertCountries(supabase, data.countries || []),
      leagues: await upsertLeagues(supabase, data.competitions || []),
      teams: await upsertTeams(supabase, data.games || []),
      matches: await upsertMatches(supabase, data.games || [])
    };

    return results;
  } catch (error) {
    console.error('❌ Failed to fetch main data:', error.message);
    throw error;
  }
}

// Step 2: Fetch and insert players for all teams
async function fetchPlayersData(supabase, maxTeams = null) {
  console.log('🚀 Step 2: Fetching players data...');
  
  try {
    // Get all teams from database
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('api_team_id, team_name')
      .not('api_team_id', 'is', null)
      .limit(maxTeams || 1000);

    if (teamsError) throw new Error(`Failed to fetch teams: ${teamsError.message}`);
    if (!teams || teams.length === 0) {
      return { success: true, teamsProcessed: 0, totalPlayersUpserted: 0 };
    }

    console.log(`📊 Processing ${teams.length} teams for player data...`);

    let totalPlayersUpserted = 0;
    let teamsProcessed = 0;
    let teamsFailed = 0;
    const failedTeams = [];

    // Process teams in batches to avoid overwhelming the API
    const batchSize = 10;
    for (let i = 0; i < teams.length; i += batchSize) {
      const batch = teams.slice(i, i + batchSize);
      console.log(`\n🏟️ Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(teams.length/batchSize)} (${batch.length} teams)`);

      for (const team of batch) {
        try {
          const result = await fetchTeamPlayers(supabase, team.api_team_id, team.team_name);
          if (result.success) {
            totalPlayersUpserted += result.count;
            teamsProcessed++;
          } else {
            teamsFailed++;
            failedTeams.push({ teamId: team.api_team_id, teamName: team.team_name, error: result.error });
          }
        } catch (error) {
          teamsFailed++;
          failedTeams.push({ teamId: team.api_team_id, teamName: team.team_name, error: error.message });
        }
        
        await delay(200); // Respectful delay between requests
      }
      
      // Longer delay between batches
      if (i + batchSize < teams.length) {
        console.log('⏳ Waiting between batches...');
        await delay(1000);
      }
    }

    return {
      success: true,
      teamsTotal: teams.length,
      teamsProcessed,
      teamsFailed,
      totalPlayersUpserted,
      failedTeams
    };
  } catch (error) {
    console.error('❌ Failed to fetch players data:', error.message);
    throw error;
  }
}

// Helper function to fetch players for a single team
async function fetchTeamPlayers(supabase, apiTeamId, teamName) {
  try {
    const SQUAD_API_URL = `https://webws.365scores.com/web/squads/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitors=${apiTeamId}`;
    
    const response = await fetch(SQUAD_API_URL);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.squads || data.squads.length === 0 || !data.squads[0].athletes) {
      console.log(`   ℹ️ No squad data for ${teamName}`);
      return { success: true, count: 0 };
    }

    const result = await upsertPlayers(supabase, data.squads[0].athletes, apiTeamId);
    console.log(`   ✅ ${teamName}: ${result.count} players`);
    return result;
  } catch (error) {
    console.error(`   ❌ ${teamName}: ${error.message}`);
    return { success: false, count: 0, error: error.message };
  }
}

// Database operation functions (condensed versions)
async function upsertCountries(supabase, countries) {
  if (!countries || countries.length === 0) return { success: true, count: 0 };
  
  const toUpsert = countries.map((c) => ({
    country_id: c.id,
    name: c.name,
    name_for_url: c.nameForURL || null,
    sport_types: c.sportTypes || null,
    image_version: safeParseInt(c.imageVersion),
    is_international: c.isInternational || false,
    total_games: safeParseInt(c.totalGames) || 0,
    live_games: safeParseInt(c.liveGames) || 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }));

  const { error } = await supabase.from('countries').upsert(toUpsert, { onConflict: 'country_id' });
  if (error) throw new Error(`Error upserting countries: ${error.message}`);
  
  console.log(`✅ Countries: ${toUpsert.length} upserted`);
  return { success: true, count: toUpsert.length };
}

async function upsertLeagues(supabase, leagues) {
  if (!leagues || leagues.length === 0) return { success: true, count: 0 };
  
  const toUpsert = leagues.map((l) => ({
    api_league_id: l.id,
    league_name: l.name,
    country_id: l.countryId,
    short_name: l.shortName || null,
    name_for_url: l.nameForURL || null,
    popularity_rank: safeParseInt(l.popularityRank),
    image_version: safeParseInt(l.imageVersion),
    current_season_num: safeParseInt(l.currentSeasonNum),
    current_stage_num: safeParseInt(l.currentStageNum),
    has_standings: l.hasStandings || false,
    has_brackets: l.hasBrackets || false,
    has_stats: l.hasStats || false,
    has_transfers: l.hasTransfers || false,
    color: l.color || null,
    is_international: l.isInternational || false,
    is_active: l.isActive !== false
  }));

  const { error } = await supabase.from('leagues').upsert(toUpsert, { onConflict: 'api_league_id' });
  if (error) throw new Error(`Error upserting leagues: ${error.message}`);
  
  console.log(`✅ Leagues: ${toUpsert.length} upserted`);
  return { success: true, count: toUpsert.length };
}

async function upsertTeams(supabase, games) {
  if (!games || games.length === 0) return { success: true, count: 0 };
  
  const teamsMap = new Map();
  games.forEach((game) => {
    if (game.homeCompetitor) teamsMap.set(game.homeCompetitor.id, game.homeCompetitor);
    if (game.awayCompetitor) teamsMap.set(game.awayCompetitor.id, game.awayCompetitor);
  });

  if (teamsMap.size === 0) return { success: true, count: 0 };

  const toUpsert = Array.from(teamsMap.values()).map((t) => ({
    api_team_id: t.id,
    team_name: t.name,
    country_id: t.countryId,
    short_name: t.shortName || null,
    symbolic_name: t.symbolicName || null,
    name_for_url: t.nameForURL || null,
    popularity_rank: safeParseInt(t.popularityRank),
    image_version: safeParseInt(t.imageVersion),
    color: t.color || null,
    away_color: t.awayColor || null,
    main_competition_id: safeParseInt(t.mainCompetitionId),
    has_squad: t.hasSquad || false,
    has_transfers: t.hasTransfers || false,
    competitor_num: safeParseInt(t.competitorNum) || 0,
    hide_on_search: t.hideOnSearch || false,
    hide_on_catalog: t.hideOnCatalog || false
  }));

  const { error } = await supabase.from('teams').upsert(toUpsert, { onConflict: 'api_team_id' });
  if (error) throw new Error(`Error upserting teams: ${error.message}`);

  console.log(`✅ Teams: ${toUpsert.length} upserted`);
  return { success: true, count: toUpsert.length };
}

async function upsertMatches(supabase, games) {
  if (!games || games.length === 0) return { success: true, count: 0 };

  // Validate foreign keys exist
  const [leaguesResult, teamsResult] = await Promise.all([
    supabase.from('leagues').select('api_league_id'),
    supabase.from('teams').select('api_team_id')
  ]);

  if (leaguesResult.error || teamsResult.error) {
    throw new Error('Failed to validate foreign keys for matches');
  }

  const existingLeagueIds = new Set(leaguesResult.data.map(l => l.api_league_id));
  const existingTeamIds = new Set(teamsResult.data.map(t => t.api_team_id));

  const validGames = games.filter((game) => {
    return existingLeagueIds.has(game.competitionId) &&
           game.homeCompetitor && existingTeamIds.has(game.homeCompetitor.id) &&
           game.awayCompetitor && existingTeamIds.has(game.awayCompetitor.id);
  });

  if (validGames.length === 0) return { success: true, count: 0 };

  const toUpsert = validGames.map((g) => ({
    api_match_id: g.id,
    home_team_id: g.homeCompetitor.id,
    away_team_id: g.awayCompetitor.id,
    competition_id: g.competitionId,
    match_date: g.startTime ? parseDate(g.startTime)?.split('T')[0] : null,
    match_time: g.startTime ? parseDate(g.startTime)?.split('T')[1]?.split('.')[0] : null,
    status: 'Upcoming',
    home_score: g.scores?.[0] !== undefined ? safeParseInt(g.scores[0]) : null,
    away_score: g.scores?.[1] !== undefined ? safeParseInt(g.scores[1]) : null,
    minute: safeParseInt(g.gameTime),
    sport_id: safeParseInt(g.sportId),
    season_num: safeParseInt(g.seasonNum),
    stage_num: safeParseInt(g.stageNum),
    round_num: safeParseInt(g.roundNum),
    round_name: g.roundName || null,
    competition_display_name: g.competitionDisplayName || null,
    start_time: parseDate(g.startTime),
    status_group: safeParseInt(g.statusGroup),
    status_text: g.statusText || null,
    short_status_text: g.shortStatusText || null,
    game_time_display: g.gameTimeDisplay || null,
    just_ended: g.justEnded || false,
    game_time: safeParseFloat(g.gameTime),
    show_countdown: g.showCountdown || false,
    has_lineups: g.hasLineups || false,
    has_stats: g.hasStats || false,
    has_standings: g.hasStandings || false,
    has_bets: g.hasBets || false,
    has_news: g.hasNews || false,
    has_video: g.hasVideo || false,
    venue_id: safeParseInt(g.venue?.id),
    is_home_away_inverted: g.isHomeAwayInverted || false,
    winner: safeParseInt(g.winner) || 0
  }));

  const { error } = await supabase.from('matches').upsert(toUpsert, { onConflict: 'api_match_id' });
  if (error) throw new Error(`Error upserting matches: ${error.message}`);

  console.log(`✅ Matches: ${toUpsert.length} upserted`);
  return { success: true, count: toUpsert.length };
}

async function upsertPlayers(supabase, players, apiTeamId) {
  if (!players || players.length === 0) return { success: true, count: 0 };

  // Filter actual players (not staff)
  const actualPlayers = players.filter((p) => {
    return p.formationPosition &&
           p.formationPosition.name !== 'Coach' &&
           p.formationPosition.name !== 'Assistant Coach' &&
           p.position &&
           p.position.name !== 'Management' &&
           !p.position.isStaff;
  });

  if (actualPlayers.length === 0) return { success: true, count: 0 };

  // Get internal team_id
  const { data: teamData, error: teamError } = await supabase
    .from('teams')
    .select('team_id')
    .eq('api_team_id', apiTeamId)
    .single();

  if (teamError || !teamData) {
    return { success: false, count: 0, error: 'Team not found' };
  }

  const toUpsert = actualPlayers.map((player) => ({
    api_player_id: player.id,
    player_name: player.name,
    team_id: teamData.team_id,
    position: player.position?.name || null,
    number: safeParseInt(player.jerseyNum),
    age: safeParseInt(player.age),
    jersey_num: safeParseInt(player.jerseyNum),
    birthdate: parseDateOnly(player.birthdate),
    gender: safeParseInt(player.gender),
    height: safeParseInt(player.height),
    short_name: player.shortName || null,
    name_for_url: player.nameForURL || null,
    sport_id: safeParseInt(player.sportId),
    club_id: safeParseInt(player.clubId),
    nationality_id: safeParseInt(player.nationalityId),
    national_team_id: safeParseInt(player.nationalTeamId),
    image_version: safeParseInt(player.imageVersion),
    formation_position_id: safeParseInt(player.formationPosition?.id)
  }));

  const { error } = await supabase.from('players').upsert(toUpsert, { onConflict: 'api_player_id' });
  if (error) throw new Error(`Error upserting players: ${error.message}`);

  return { success: true, count: toUpsert.length };
}

// Main handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();
  let mainDataResults = null;
  let playersDataResults = null;

  try {
    console.log('🚀 Starting MASTER football data pipeline...');

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Parse query parameters
    const url = new URL(req.url);
    const skipPlayers = url.searchParams.get('skipPlayers') === 'true';
    const maxTeams = url.searchParams.get('maxTeams') ? parseInt(url.searchParams.get('maxTeams')) : null;

    // Step 1: Fetch main data
    mainDataResults = await fetchMainData(supabase);

    // Step 2: Fetch players data (unless skipped)
    if (!skipPlayers) {
      console.log('\n⏳ Waiting before fetching players data...');
      await delay(2000); // Brief pause between major operations
      playersDataResults = await fetchPlayersData(supabase, maxTeams);
    } else {
      console.log('⏭️ Skipping players data fetch as requested');
      playersDataResults = { success: true, message: 'Skipped as requested' };
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    const totalPlayers = playersDataResults.totalPlayersUpserted || 0;
    const totalMatches = mainDataResults.matches.count;
    const totalTeams = mainDataResults.teams.count;

    const successMessage = `🎉 MASTER pipeline completed successfully in ${duration}s!
📈 Complete Results:
  • Countries: ${mainDataResults.countries.count}
  • Leagues: ${mainDataResults.leagues.count}
  • Teams: ${totalTeams}
  • Matches: ${totalMatches}
  • Players: ${totalPlayers}`;

    console.log(successMessage);

    return new Response(JSON.stringify({
      success: true,
      message: 'Master football data pipeline completed successfully!',
      duration: `${duration}s`,
      mainData: mainDataResults,
      playersData: playersDataResults,
      summary: {
        countries: mainDataResults.countries.count,
        leagues: mainDataResults.leagues.count,
        teams: totalTeams,
        matches: totalMatches,
        players: totalPlayers
      },
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.error(`❌ Master pipeline failed after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      partialResults: {
        mainData: mainDataResults,
        playersData: playersDataResults
      },
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
