// Optimized Players Function - Fast Batch Processing to Avoid Timeouts
// This function processes all teams in parallel batches to minimize execution time

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Optimized delay - much shorter
const delay = (ms) => new Promise((res) => setTimeout(res, ms));

// Helper functions
function safeParseInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
}

function parseDate(dateString) {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
  } catch {
    return null;
  }
}

// Optimized function to fetch and process players for a single team
async function processTeamPlayers(supabase, teamId, teamName) {
  try {
    const SQUAD_API_URL = `https://webws.365scores.com/web/squads/?appTypeId=5&langId=1&timezoneName=Africa/Casablanca&userCountryId=127&competitors=${teamId}`;
    
    const response = await fetch(SQUAD_API_URL);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.squads || data.squads.length === 0 || !data.squads[0].athletes) {
      return { success: true, count: 0, teamId, teamName, note: 'No squad data' };
    }

    const players = data.squads[0].athletes;
    
    // Filter actual players (not staff)
    const actualPlayers = players.filter((p) => {
      const isPlayer = p.formationPosition && 
                      p.formationPosition.name !== 'Coach' && 
                      p.formationPosition.name !== 'Assistant Coach' &&
                      p.position && 
                      p.position.name !== 'Management' &&
                      !p.position.isStaff;
      return isPlayer;
    });

    if (actualPlayers.length === 0) {
      return { success: true, count: 0, teamId, teamName, note: 'No actual players after filtering' };
    }

    // Prepare players data for insertion
    const playersToUpsert = actualPlayers.map((player) => ({
      api_player_id: player.id,
      player_name: player.name,
      team_id: teamId, // Use api_team_id as per foreign key constraint
      position: player.position?.name || null,
      number: safeParseInt(player.jerseyNum),
      nationality: null,
      image_url: null,
      cover_image_url: null,
      bio: null,
      age: safeParseInt(player.age),
      jersey_num: safeParseInt(player.jerseyNum),
      birthdate: parseDate(player.birthdate),
      gender: safeParseInt(player.gender),
      height: safeParseInt(player.height),
      short_name: player.shortName || null,
      name_for_url: player.nameForURL || null,
      sport_id: safeParseInt(player.sportId),
      club_id: safeParseInt(player.clubId),
      nationality_id: safeParseInt(player.nationalityId),
      national_team_id: safeParseInt(player.nationalTeamId),
      image_version: safeParseInt(player.imageVersion),
      formation_position_id: safeParseInt(player.formationPosition?.id),
      status: null
    }));

    // Insert players for this team
    const { error } = await supabase
      .from('players')
      .upsert(playersToUpsert, { onConflict: 'api_player_id' });

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    return { 
      success: true, 
      count: playersToUpsert.length, 
      teamId, 
      teamName,
      players: playersToUpsert.length
    };

  } catch (error) {
    return { 
      success: false, 
      count: 0, 
      teamId, 
      teamName, 
      error: error.message 
    };
  }
}

// Process teams in parallel batches
async function processBatch(supabase, teams, batchNumber, totalBatches) {
  console.log(`🚀 Processing batch ${batchNumber}/${totalBatches} (${teams.length} teams in parallel)`);
  
  const promises = teams.map(team => 
    processTeamPlayers(supabase, team.api_team_id, team.team_name)
  );
  
  const results = await Promise.allSettled(promises);
  
  const processedResults = results.map((result, index) => {
    if (result.status === 'fulfilled') {
      const res = result.value;
      if (res.success) {
        console.log(`   ✅ ${res.teamName}: ${res.count} players`);
      } else {
        console.log(`   ❌ ${res.teamName}: ${res.error}`);
      }
      return res;
    } else {
      const team = teams[index];
      console.log(`   ❌ ${team.team_name}: Promise rejected - ${result.reason}`);
      return {
        success: false,
        count: 0,
        teamId: team.api_team_id,
        teamName: team.team_name,
        error: result.reason?.message || 'Promise rejected'
      };
    }
  });
  
  return processedResults;
}

// Main handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const startTime = Date.now();
  let totalPlayersUpserted = 0;
  let teamsProcessed = 0;
  let teamsFailed = 0;
  const allResults = [];

  try {
    console.log('🚀 Starting OPTIMIZED players insertion (fast batch processing)...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '', 
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get query parameters
    const url = new URL(req.url);
    const maxTeams = parseInt(url.searchParams.get('maxTeams') || '0'); // 0 = all teams
    const batchSize = parseInt(url.searchParams.get('batchSize') || '20'); // Process 20 teams in parallel
    const delayMs = parseInt(url.searchParams.get('delay') || '100'); // Minimal delay between batches

    // Get all teams with squads
    let teamsQuery = supabase
      .from('teams')
      .select('api_team_id, team_name, has_squad')
      .not('api_team_id', 'is', null)
      .eq('has_squad', true); // Only teams that have squads

    if (maxTeams > 0) {
      teamsQuery = teamsQuery.limit(maxTeams);
    }

    const { data: teams, error: teamsError } = await teamsQuery;

    if (teamsError) {
      throw new Error(`Failed to fetch teams: ${teamsError.message}`);
    }

    if (!teams || teams.length === 0) {
      return new Response(JSON.stringify({
        success: true,
        message: 'No teams found with squads',
        summary: { teamsTotal: 0, teamsProcessed: 0, teamsFailed: 0, totalPlayersUpserted: 0 }
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    console.log(`🎯 Processing ${teams.length} teams in batches of ${batchSize}...`);

    // Split teams into batches
    const batches = [];
    for (let i = 0; i < teams.length; i += batchSize) {
      batches.push(teams.slice(i, i + batchSize));
    }

    console.log(`📦 Created ${batches.length} batches for parallel processing`);

    // Process each batch
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const batchResults = await processBatch(supabase, batch, i + 1, batches.length);
      
      // Aggregate results
      batchResults.forEach(result => {
        allResults.push(result);
        if (result.success) {
          totalPlayersUpserted += result.count;
          teamsProcessed++;
        } else {
          teamsFailed++;
        }
      });

      // Short delay between batches (much shorter than before)
      if (i < batches.length - 1) {
        console.log(`⏳ Brief pause (${delayMs}ms)...`);
        await delay(delayMs);
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    const summary = {
      teamsTotal: teams.length,
      teamsProcessed,
      teamsFailed,
      totalPlayersUpserted,
      duration: `${duration}s`,
      successRate: `${Math.round((teamsProcessed / teams.length) * 100)}%`,
      averagePlayersPerTeam: Math.round(totalPlayersUpserted / teamsProcessed) || 0,
      processingSpeed: `${Math.round(teams.length / duration)} teams/second`
    };

    console.log(`\n🎉 OPTIMIZED processing completed in ${duration}s!`);
    console.log(`📊 Results: ${teamsProcessed}/${teams.length} teams successful`);
    console.log(`👥 Total players inserted: ${totalPlayersUpserted}`);
    console.log(`⚡ Processing speed: ${summary.processingSpeed}`);

    return new Response(JSON.stringify({
      success: true,
      message: 'Optimized players insertion completed successfully',
      summary,
      results: allResults,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.error(`❌ Optimized processing failed after ${duration}s:`, error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      duration: `${duration}s`,
      partialResults: {
        teamsProcessed,
        teamsFailed,
        totalPlayersUpserted,
        results: allResults
      },
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
