// Test Script for Football Data Functions
// This script tests the complete data flow and validates the results

// Configuration - Update these with your Supabase project details
const SUPABASE_URL = 'https://your-project.supabase.co';
const FUNCTIONS_BASE_URL = `${SUPABASE_URL}/functions/v1`;

// Test configuration
const TEST_CONFIG = {
  maxTeams: 5, // Limit teams for testing
  skipPlayers: false, // Set to true to skip players for faster testing
  validateAfterEach: true // Run validation after each step
};

// Helper function to make API calls
async function callFunction(functionName, params = {}) {
  const url = new URL(`${FUNCTIONS_BASE_URL}/${functionName}`);
  Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
  
  console.log(`🚀 Calling ${functionName}...`);
  const startTime = Date.now();
  
  try {
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    const duration = Date.now() - startTime;
    const result = await response.json();
    
    if (response.ok) {
      console.log(`✅ ${functionName} completed in ${duration}ms`);
      return { success: true, data: result, duration };
    } else {
      console.error(`❌ ${functionName} failed:`, result.error);
      return { success: false, error: result.error, duration };
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ ${functionName} error:`, error.message);
    return { success: false, error: error.message, duration };
  }
}

// Test individual functions
async function testMainDataFunction() {
  console.log('\n📊 Testing Main Data Function...');
  const result = await callFunction('fetchdata_function_edge');
  
  if (result.success) {
    const data = result.data;
    console.log(`   Countries: ${data.results?.countries?.count || 0}`);
    console.log(`   Leagues: ${data.results?.leagues?.count || 0}`);
    console.log(`   Teams: ${data.results?.teams?.count || 0}`);
    console.log(`   Matches: ${data.results?.matches?.count || 0}`);
  }
  
  return result;
}

async function testPlayersFunction() {
  console.log('\n👥 Testing Players Function...');
  const result = await callFunction('fetch_players_function_edge');
  
  if (result.success) {
    const data = result.data;
    console.log(`   Teams Processed: ${data.results?.teamsProcessed || 0}`);
    console.log(`   Teams Failed: ${data.results?.teamsFailed || 0}`);
    console.log(`   Total Players: ${data.results?.totalPlayersUpserted || 0}`);
  }
  
  return result;
}

async function testMasterFunction() {
  console.log('\n🎯 Testing Master Pipeline Function...');
  const params = {};
  if (TEST_CONFIG.maxTeams) params.maxTeams = TEST_CONFIG.maxTeams;
  if (TEST_CONFIG.skipPlayers) params.skipPlayers = 'true';
  
  const result = await callFunction('master_football_data_function', params);
  
  if (result.success) {
    const data = result.data;
    console.log(`   Summary:`, data.summary);
    if (data.playersData && !TEST_CONFIG.skipPlayers) {
      console.log(`   Players Processing: ${data.playersData.teamsProcessed}/${data.playersData.teamsTotal} teams`);
    }
  }
  
  return result;
}

async function testValidationFunction() {
  console.log('\n🔍 Testing Validation Function...');
  const result = await callFunction('data_validation_logging_function');
  
  if (result.success) {
    const data = result.data;
    console.log(`   Data Summary:`, data.summary);
    
    // Check for issues
    let totalIssues = 0;
    Object.values(data.validation || {}).forEach(v => {
      if (v.issues) totalIssues += v.issues.length;
    });
    
    if (totalIssues > 0) {
      console.warn(`   ⚠️ Found ${totalIssues} data quality issues`);
      if (data.recommendations) {
        console.log(`   💡 Recommendations:`, data.recommendations.slice(0, 3));
      }
    } else {
      console.log(`   ✅ No data quality issues found`);
    }
  }
  
  return result;
}

// Main test runner
async function runTests() {
  console.log('🧪 Starting Football Data Functions Test Suite');
  console.log('=' .repeat(60));
  
  const testResults = {
    startTime: Date.now(),
    tests: []
  };
  
  try {
    // Test 1: Master Pipeline (recommended approach)
    console.log('\n🎯 TEST 1: Master Pipeline Function');
    const masterResult = await testMasterFunction();
    testResults.tests.push({ name: 'Master Pipeline', ...masterResult });
    
    if (TEST_CONFIG.validateAfterEach && masterResult.success) {
      const validationResult = await testValidationFunction();
      testResults.tests.push({ name: 'Validation After Master', ...validationResult });
    }
    
    // Test 2: Individual Functions (alternative approach)
    if (!masterResult.success) {
      console.log('\n📊 TEST 2: Individual Functions (Fallback)');
      
      const mainResult = await testMainDataFunction();
      testResults.tests.push({ name: 'Main Data', ...mainResult });
      
      if (mainResult.success && !TEST_CONFIG.skipPlayers) {
        // Wait a bit before players function
        console.log('⏳ Waiting before players function...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const playersResult = await testPlayersFunction();
        testResults.tests.push({ name: 'Players Data', ...playersResult });
      }
      
      if (TEST_CONFIG.validateAfterEach) {
        const validationResult = await testValidationFunction();
        testResults.tests.push({ name: 'Final Validation', ...validationResult });
      }
    }
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    testResults.error = error.message;
  }
  
  // Generate test report
  testResults.endTime = Date.now();
  testResults.totalDuration = testResults.endTime - testResults.startTime;
  
  console.log('\n' + '=' .repeat(60));
  console.log('📋 TEST RESULTS SUMMARY');
  console.log('=' .repeat(60));
  
  const successful = testResults.tests.filter(t => t.success).length;
  const failed = testResults.tests.filter(t => !t.success).length;
  
  console.log(`Total Tests: ${testResults.tests.length}`);
  console.log(`Successful: ${successful}`);
  console.log(`Failed: ${failed}`);
  console.log(`Total Duration: ${testResults.totalDuration}ms`);
  
  if (failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests.filter(t => !t.success).forEach(test => {
      console.log(`   - ${test.name}: ${test.error}`);
    });
  }
  
  if (successful === testResults.tests.length) {
    console.log('\n🎉 All tests passed! Your football data functions are working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the errors above and your function configurations.');
  }
  
  return testResults;
}

// Usage instructions
function printUsageInstructions() {
  console.log('🔧 SETUP INSTRUCTIONS:');
  console.log('1. Update SUPABASE_URL in this script with your project URL');
  console.log('2. Deploy your Edge Functions to Supabase');
  console.log('3. Ensure your database schema matches the requirements');
  console.log('4. Run this script: node test_data_flow.js');
  console.log('');
  console.log('📝 TEST CONFIGURATION:');
  console.log(`   Max Teams: ${TEST_CONFIG.maxTeams} (for faster testing)`);
  console.log(`   Skip Players: ${TEST_CONFIG.skipPlayers}`);
  console.log(`   Validate After Each: ${TEST_CONFIG.validateAfterEach}`);
  console.log('');
}

// Run the tests
if (require.main === module) {
  printUsageInstructions();
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testMainDataFunction,
  testPlayersFunction,
  testMasterFunction,
  testValidationFunction
};
