// Data Validation and Logging Function for Football Data Pipeline
// This function provides comprehensive validation, logging, and monitoring
// for the football data insertion process

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Data validation functions
async function validateDatabaseIntegrity(supabase) {
  console.log('🔍 Starting comprehensive database integrity validation...');
  
  const results = {
    countries: await validateCountries(supabase),
    leagues: await validateLeagues(supabase),
    teams: await validateTeams(supabase),
    matches: await validateMatches(supabase),
    players: await validatePlayers(supabase),
    foreignKeys: await validateForeignKeys(supabase)
  };

  return results;
}

async function validateCountries(supabase) {
  try {
    const { data, error } = await supabase
      .from('countries')
      .select('country_id, name, is_international, total_games, live_games');
    
    if (error) throw error;

    const validation = {
      total: data.length,
      issues: [],
      summary: {}
    };

    // Check for missing required fields
    const missingNames = data.filter(c => !c.name || c.name.trim() === '');
    if (missingNames.length > 0) {
      validation.issues.push(`${missingNames.length} countries missing names`);
    }

    // Check data consistency
    const internationalCount = data.filter(c => c.is_international).length;
    const regularCount = data.length - internationalCount;
    
    validation.summary = {
      total: data.length,
      international: internationalCount,
      regular: regularCount,
      withGames: data.filter(c => c.total_games > 0).length,
      liveGames: data.filter(c => c.live_games > 0).length
    };

    console.log(`✅ Countries validation: ${data.length} total, ${validation.issues.length} issues`);
    return validation;
  } catch (error) {
    console.error('❌ Countries validation failed:', error.message);
    return { error: error.message };
  }
}

async function validateLeagues(supabase) {
  try {
    const { data, error } = await supabase
      .from('leagues')
      .select('api_league_id, league_name, country_id, is_international, has_standings, is_active');
    
    if (error) throw error;

    const validation = {
      total: data.length,
      issues: [],
      summary: {}
    };

    // Check for missing required fields
    const missingNames = data.filter(l => !l.league_name || l.league_name.trim() === '');
    if (missingNames.length > 0) {
      validation.issues.push(`${missingNames.length} leagues missing names`);
    }

    const missingCountries = data.filter(l => !l.country_id);
    if (missingCountries.length > 0) {
      validation.issues.push(`${missingCountries.length} leagues missing country_id`);
    }

    validation.summary = {
      total: data.length,
      international: data.filter(l => l.is_international).length,
      withStandings: data.filter(l => l.has_standings).length,
      active: data.filter(l => l.is_active).length,
      uniqueCountries: new Set(data.map(l => l.country_id)).size
    };

    console.log(`✅ Leagues validation: ${data.length} total, ${validation.issues.length} issues`);
    return validation;
  } catch (error) {
    console.error('❌ Leagues validation failed:', error.message);
    return { error: error.message };
  }
}

async function validateTeams(supabase) {
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('api_team_id, team_name, country_id, has_squad, main_competition_id');
    
    if (error) throw error;

    const validation = {
      total: data.length,
      issues: [],
      summary: {}
    };

    // Check for missing required fields
    const missingNames = data.filter(t => !t.team_name || t.team_name.trim() === '');
    if (missingNames.length > 0) {
      validation.issues.push(`${missingNames.length} teams missing names`);
    }

    const missingApiIds = data.filter(t => !t.api_team_id);
    if (missingApiIds.length > 0) {
      validation.issues.push(`${missingApiIds.length} teams missing api_team_id`);
    }

    validation.summary = {
      total: data.length,
      withSquads: data.filter(t => t.has_squad).length,
      withCountries: data.filter(t => t.country_id).length,
      withCompetitions: data.filter(t => t.main_competition_id).length,
      uniqueCountries: new Set(data.filter(t => t.country_id).map(t => t.country_id)).size
    };

    console.log(`✅ Teams validation: ${data.length} total, ${validation.issues.length} issues`);
    return validation;
  } catch (error) {
    console.error('❌ Teams validation failed:', error.message);
    return { error: error.message };
  }
}

async function validateMatches(supabase) {
  try {
    const { data, error } = await supabase
      .from('matches')
      .select('api_match_id, home_team_id, away_team_id, competition_id, start_time, status_text');
    
    if (error) throw error;

    const validation = {
      total: data.length,
      issues: [],
      summary: {}
    };

    // Check for missing required fields
    const missingHomeTeams = data.filter(m => !m.home_team_id);
    if (missingHomeTeams.length > 0) {
      validation.issues.push(`${missingHomeTeams.length} matches missing home_team_id`);
    }

    const missingAwayTeams = data.filter(m => !m.away_team_id);
    if (missingAwayTeams.length > 0) {
      validation.issues.push(`${missingAwayTeams.length} matches missing away_team_id`);
    }

    const missingCompetitions = data.filter(m => !m.competition_id);
    if (missingCompetitions.length > 0) {
      validation.issues.push(`${missingCompetitions.length} matches missing competition_id`);
    }

    // Check for logical issues
    const sameTeamMatches = data.filter(m => m.home_team_id === m.away_team_id);
    if (sameTeamMatches.length > 0) {
      validation.issues.push(`${sameTeamMatches.length} matches with same home/away team`);
    }

    validation.summary = {
      total: data.length,
      withStartTime: data.filter(m => m.start_time).length,
      withStatus: data.filter(m => m.status_text).length,
      uniqueCompetitions: new Set(data.filter(m => m.competition_id).map(m => m.competition_id)).size,
      uniqueTeams: new Set([
        ...data.filter(m => m.home_team_id).map(m => m.home_team_id),
        ...data.filter(m => m.away_team_id).map(m => m.away_team_id)
      ]).size
    };

    console.log(`✅ Matches validation: ${data.length} total, ${validation.issues.length} issues`);
    return validation;
  } catch (error) {
    console.error('❌ Matches validation failed:', error.message);
    return { error: error.message };
  }
}

async function validatePlayers(supabase) {
  try {
    const { data, error } = await supabase
      .from('players')
      .select('api_player_id, player_name, team_id, position, age, nationality_id');
    
    if (error) throw error;

    const validation = {
      total: data.length,
      issues: [],
      summary: {}
    };

    // Check for missing required fields
    const missingNames = data.filter(p => !p.player_name || p.player_name.trim() === '');
    if (missingNames.length > 0) {
      validation.issues.push(`${missingNames.length} players missing names`);
    }

    const missingTeams = data.filter(p => !p.team_id);
    if (missingTeams.length > 0) {
      validation.issues.push(`${missingTeams.length} players missing team_id`);
    }

    const missingApiIds = data.filter(p => !p.api_player_id);
    if (missingApiIds.length > 0) {
      validation.issues.push(`${missingApiIds.length} players missing api_player_id`);
    }

    // Check data quality
    const invalidAges = data.filter(p => p.age && (p.age < 15 || p.age > 50));
    if (invalidAges.length > 0) {
      validation.issues.push(`${invalidAges.length} players with unusual ages`);
    }

    validation.summary = {
      total: data.length,
      withPositions: data.filter(p => p.position).length,
      withAges: data.filter(p => p.age).length,
      withNationalities: data.filter(p => p.nationality_id).length,
      uniqueTeams: new Set(data.filter(p => p.team_id).map(p => p.team_id)).size,
      uniquePositions: new Set(data.filter(p => p.position).map(p => p.position)).size,
      averageAge: data.filter(p => p.age).reduce((sum, p) => sum + p.age, 0) / data.filter(p => p.age).length || 0
    };

    console.log(`✅ Players validation: ${data.length} total, ${validation.issues.length} issues`);
    return validation;
  } catch (error) {
    console.error('❌ Players validation failed:', error.message);
    return { error: error.message };
  }
}

async function validateForeignKeys(supabase) {
  console.log('🔗 Validating foreign key relationships...');
  
  try {
    const issues = [];

    // Check leagues -> countries
    const { data: leaguesWithoutCountries } = await supabase
      .from('leagues')
      .select('api_league_id, league_name, country_id')
      .not('country_id', 'is', null)
      .not('country_id', 'in', 
        `(${await supabase.from('countries').select('country_id').then(r => r.data.map(c => c.country_id).join(','))})`
      );
    
    if (leaguesWithoutCountries && leaguesWithoutCountries.length > 0) {
      issues.push(`${leaguesWithoutCountries.length} leagues reference non-existent countries`);
    }

    // Check teams -> countries
    const { data: teamsWithoutCountries } = await supabase
      .from('teams')
      .select('api_team_id, team_name, country_id')
      .not('country_id', 'is', null)
      .not('country_id', 'in', 
        `(${await supabase.from('countries').select('country_id').then(r => r.data.map(c => c.country_id).join(','))})`
      );
    
    if (teamsWithoutCountries && teamsWithoutCountries.length > 0) {
      issues.push(`${teamsWithoutCountries.length} teams reference non-existent countries`);
    }

    // Check matches -> leagues
    const { data: matchesWithoutLeagues } = await supabase
      .from('matches')
      .select('api_match_id, competition_id')
      .not('competition_id', 'is', null)
      .not('competition_id', 'in', 
        `(${await supabase.from('leagues').select('api_league_id').then(r => r.data.map(l => l.api_league_id).join(','))})`
      );
    
    if (matchesWithoutLeagues && matchesWithoutLeagues.length > 0) {
      issues.push(`${matchesWithoutLeagues.length} matches reference non-existent leagues`);
    }

    // Check matches -> teams (home)
    const { data: matchesWithoutHomeTeams } = await supabase
      .from('matches')
      .select('api_match_id, home_team_id')
      .not('home_team_id', 'is', null)
      .not('home_team_id', 'in', 
        `(${await supabase.from('teams').select('api_team_id').then(r => r.data.map(t => t.api_team_id).join(','))})`
      );
    
    if (matchesWithoutHomeTeams && matchesWithoutHomeTeams.length > 0) {
      issues.push(`${matchesWithoutHomeTeams.length} matches reference non-existent home teams`);
    }

    // Check players -> teams
    const { data: playersWithoutTeams } = await supabase
      .from('players')
      .select('api_player_id, player_name, team_id')
      .not('team_id', 'is', null)
      .not('team_id', 'in', 
        `(${await supabase.from('teams').select('team_id').then(r => r.data.map(t => t.team_id).join(','))})`
      );
    
    if (playersWithoutTeams && playersWithoutTeams.length > 0) {
      issues.push(`${playersWithoutTeams.length} players reference non-existent teams`);
    }

    console.log(`✅ Foreign keys validation: ${issues.length} issues found`);
    return { issues, valid: issues.length === 0 };
  } catch (error) {
    console.error('❌ Foreign keys validation failed:', error.message);
    return { error: error.message };
  }
}

// Generate comprehensive report
async function generateDataReport(supabase) {
  console.log('📊 Generating comprehensive data report...');
  
  try {
    const validation = await validateDatabaseIntegrity(supabase);
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        countries: validation.countries.total || 0,
        leagues: validation.leagues.total || 0,
        teams: validation.teams.total || 0,
        matches: validation.matches.total || 0,
        players: validation.players.total || 0
      },
      validation: validation,
      recommendations: generateRecommendations(validation)
    };

    return report;
  } catch (error) {
    console.error('❌ Report generation failed:', error.message);
    return { error: error.message };
  }
}

function generateRecommendations(validation) {
  const recommendations = [];

  // Check data completeness
  if (validation.teams.total > 0 && validation.players.total === 0) {
    recommendations.push('No players found. Run the players fetch function to populate player data.');
  }

  if (validation.matches.total === 0 && validation.teams.total > 0) {
    recommendations.push('No matches found despite having teams. Check the matches data fetch process.');
  }

  // Check data quality issues
  Object.values(validation).forEach(v => {
    if (v.issues && v.issues.length > 0) {
      v.issues.forEach(issue => {
        recommendations.push(`Data quality issue: ${issue}`);
      });
    }
  });

  // Check foreign key issues
  if (validation.foreignKeys.issues && validation.foreignKeys.issues.length > 0) {
    recommendations.push('Foreign key constraint violations detected. Review data insertion order.');
  }

  return recommendations;
}

// Main handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('🚀 Starting data validation and logging process...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '', 
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const report = await generateDataReport(supabase);

    console.log('✅ Data validation and reporting completed successfully');

    return new Response(JSON.stringify(report), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    console.error('❌ Validation process failed:', error.message);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
